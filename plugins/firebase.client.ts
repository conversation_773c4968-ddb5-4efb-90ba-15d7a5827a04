import { initializeApp } from "firebase/app";
import { getFirestore } from "firebase/firestore";

export default defineNuxtPlugin(() => {
  const config = useRuntimeConfig();
  const firebaseConfig = config.public.firebaseConfig;

  if (!firebaseConfig) {
    throw new Error(
      "Missing Firebase config in runtimeConfig.public.firebaseConfig"
    );
  }

  const app = initializeApp(firebaseConfig);
  const fs = getFirestore(app);
  console.log("[Firebase] Firestore initialized");

  return {
    provide: {
      fs: fs,
    },
  };
});
