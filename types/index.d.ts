export type Restaurant = {
  id: string;
  businessInfo: RestaurantBusinessInfo;
  name: string;
  place_id: string;
  address: string;
  photos: string[];
  rating: number;
  user_id: string;
  coordinates: RestaurantCoordinates;
  types: string[];
  createdAt: string;
  lastUpdated: string;
  menuUrl: string;
  photoUrl?: string[];
  photos?: RestaurantPhotos[];
  distance?: string;
  estimatedTime?: string;
};

export type Filter = {
  label: string;
  value: string | string[] | null | undefined;
};

export type RestaurantCoordinates = {
  lat: number;
  lng: number;
};

export type RestaurantBusinessInfo = {
  googleUrl: string;
  name: string;
  summary?: string;
  website?: string;
};

export type RestaurantPhotos = {
  downloadUrl: string;
  isCover: boolean;
  photo_id: string;
  storagePath: string;
};
