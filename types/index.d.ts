import type { GeoPoint } from "firebase-admin/firestore";

export type Restaurant = {
  address: string;
  businessInfo: RestaurantBusinessInfo;
  location: GeoPoint;
  createdAt: string;
  lastUpdated: string;
  menuUrl: string;
  name: string;
  photoUrl?: string[];
  photos?: RestaurantPhotos[];
  place_id: string;
  rating: number;
  types: string[];
  id: string;
  user_id: string;
  opening_hours: string[];

  // Computed properties
  distance?: string;
  estimatedTime?: string;
};

export type Filter = {
  label: string;
  value: string | string[] | null | undefined;
};

export type RestaurantCoordinates = {
  lat: number;
  lng: number;
};

export type RestaurantBusinessInfo = {
  googleUrl: string;
  name: string;
  summary?: string;
  website?: string;
};

export type RestaurantPhotos = {
  downloadUrl: string;
  isCover: boolean;
  photo_id: string;
  storagePath: string;
};

export type RestaurantSource = "google";

export type RestaurantReview = {
  author_name: string;
  createdAt: string;
  place_id: string;
  rating: number;
  source: RestaurantSource;
  text: string;
  time: number;
};
