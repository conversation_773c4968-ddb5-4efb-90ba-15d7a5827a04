<template>
  <div v-if="shouldShowPrompt" class="location-permission">
    <div class="location-permission__overlay" @click="handleDismiss"></div>
    <div class="location-permission__modal">
      <div class="location-permission__header">
        <div class="location-permission__icon">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" />
            <circle cx="12" cy="10" r="3" />
          </svg>
        </div>
        <h3 class="location-permission__title">Find Restaurants Near You</h3>
        <p class="location-permission__subtitle">
          Allow location access to see accurate distances and delivery times for restaurants in your area.
        </p>
      </div>

      <div class="location-permission__benefits">
        <div class="location-permission__benefit">
          <div class="location-permission__benefit-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="12" cy="12" r="10" />
              <polyline points="12,6 12,12 16,14" />
            </svg>
          </div>
          <span>Accurate delivery times</span>
        </div>
        <div class="location-permission__benefit">
          <div class="location-permission__benefit-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" />
              <circle cx="12" cy="10" r="3" />
            </svg>
          </div>
          <span>Restaurants sorted by distance</span>
        </div>
        <div class="location-permission__benefit">
          <div class="location-permission__benefit-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
            </svg>
          </div>
          <span>Personalized recommendations</span>
        </div>
      </div>

      <div class="location-permission__actions">
        <button class="location-permission__btn location-permission__btn--primary" @click="handleAllow"
          :disabled="isLoading">
          <svg v-if="isLoading" class="location-permission__spinner" viewBox="0 0 24 24">
            <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
              stroke-dasharray="31.416" stroke-dashoffset="31.416">
              <animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416"
                repeatCount="indefinite" />
              <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite" />
            </circle>
          </svg>
          {{ isLoading ? 'Getting Location...' : 'Allow Location Access' }}
        </button>
        <button class="location-permission__btn location-permission__btn--secondary" @click="handleDeny"
          :disabled="isLoading">
          Use Default Location
        </button>
      </div>

      <p class="location-permission__privacy">
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" />
        </svg>
        Your location is only used to calculate distances and is never stored or shared.
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
const locationStore = useLocationStore()
const {
  isLoading,
  hasAskedPermission,
  isLocationEnabled,
  locationError
} = storeToRefs(locationStore)

const showPrompt = ref(false)

// Show prompt if we haven't asked permission and location is not enabled
const shouldShowPrompt = computed(() => {
  return showPrompt.value && !hasAskedPermission.value && !isLocationEnabled.value
})

const handleAllow = async () => {
  const success = await locationStore.requestUserLocation()
  if (success) {
    showPrompt.value = false
    // Trigger restaurant recalculation
    const restaurantsStore = useRestaurantsStore()
    restaurantsStore.recalculateDistances()
  }
}

const handleDeny = () => {
  showPrompt.value = false
  // Mark as asked so we don't show again
  locationStore.hasAskedPermission = true
}

const handleDismiss = () => {
  showPrompt.value = false
}

// Auto-show prompt after a short delay when component mounts
onMounted(() => {
  setTimeout(() => {
    if (!hasAskedPermission.value && !isLocationEnabled.value) {
      showPrompt.value = true
    }
  }, 2000) // Show after 2 seconds
})

// Expose method to manually show prompt
const showLocationPrompt = () => {
  showPrompt.value = true
}

defineExpose({
  showLocationPrompt
})
</script>

<style lang="scss" scoped>
.location-permission {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: $z-modal;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-lg;

  &__overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
  }

  &__modal {
    position: relative;
    background: $white;
    border-radius: $border-radius-xl;
    padding: $spacing-xxl;
    max-width: 480px;
    width: 100%;
    box-shadow: $shadow-heavy;
    animation: slideUp 0.3s ease-out;

    @media (max-width: $mobile) {
      padding: $spacing-xl;
      margin: $spacing-md;
    }
  }

  &__header {
    text-align: center;
    margin-bottom: $spacing-xl;
  }

  &__icon {
    width: 64px;
    height: 64px;
    margin: 0 auto $spacing-lg auto;
    color: $primary-orange;

    svg {
      width: 100%;
      height: 100%;
    }
  }

  &__title {
    font-family: $font-primary;
    font-size: $font-2xl;
    font-weight: $font-bold;
    color: $text-primary;
    margin-bottom: $spacing-sm;
  }

  &__subtitle {
    font-family: $font-secondary;
    color: $text-secondary;
    line-height: $leading-relaxed;
  }

  &__benefits {
    margin-bottom: $spacing-xl;
  }

  &__benefit {
    display: flex;
    align-items: center;
    gap: $spacing-md;
    margin-bottom: $spacing-md;
    font-family: $font-secondary;
    color: $text-primary;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__benefit-icon {
    width: 24px;
    height: 24px;
    color: $secondary-green;
    flex-shrink: 0;

    svg {
      width: 100%;
      height: 100%;
    }
  }

  &__actions {
    display: flex;
    flex-direction: column;
    gap: $spacing-md;
    margin-bottom: $spacing-lg;
  }

  &__btn {
    padding: $spacing-md $spacing-lg;
    border-radius: $border-radius-lg;
    font-family: $font-secondary;
    font-weight: $font-semibold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: $spacing-sm;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    &--primary {
      background: $primary-orange;
      color: $white;
      border: none;

      &:hover:not(:disabled) {
        background: color.adjust($primary-orange, $lightness: -10%);
        transform: translateY(-2px);
      }
    }

    &--secondary {
      background: transparent;
      color: $text-secondary;
      border: 1px solid $border-light;

      &:hover:not(:disabled) {
        background: $bg-secondary;
        border-color: $border-medium;
      }
    }
  }

  &__spinner {
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
  }

  &__privacy {
    display: flex;
    align-items: flex-start;
    gap: $spacing-sm;
    font-family: $font-secondary;
    font-size: $font-xs;
    color: $text-muted;
    line-height: $leading-relaxed;

    svg {
      width: 16px;
      height: 16px;
      flex-shrink: 0;
      margin-top: 2px;
    }
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
</style>
