<template>
  <div class="error-state" :class="{ 'error-state--fullscreen': fullscreen }">
    <div class="container" v-if="!fullscreen">
      <div class="error-state__content">
        <div class="error-state__icon" v-if="showIcon">
          <svg v-if="type === '404'" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="11" cy="11" r="8" />
            <path d="m21 21-4.35-4.35" />
          </svg>
          <svg v-else-if="type === 'network'" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z" />
            <line x1="12" y1="9" x2="12" y2="13" />
            <line x1="12" y1="17" x2="12.01" y2="17" />
          </svg>
          <svg v-else-if="type === 'server'" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <rect x="2" y="3" width="20" height="14" rx="2" ry="2" />
            <line x1="8" y1="21" x2="16" y2="21" />
            <line x1="12" y1="17" x2="12" y2="21" />
            <line x1="7" y1="7" x2="7.01" y2="7" />
            <line x1="12" y1="7" x2="12.01" y2="7" />
            <line x1="17" y1="7" x2="17.01" y2="7" />
          </svg>
          <svg v-else viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="12" cy="12" r="10" />
            <line x1="15" y1="9" x2="9" y2="15" />
            <line x1="9" y1="9" x2="15" y2="15" />
          </svg>
        </div>

        <div class="error-state__text">
          <h1 class="error-state__title">{{ title }}</h1>
          <p class="error-state__message">{{ message }}</p>
          <p v-if="details" class="error-state__details">{{ details }}</p>
        </div>

        <div class="error-state__actions" v-if="showActions || $slots.actions">
          <!-- Custom actions slot -->
          <slot name="actions">
            <!-- Default actions -->
            <button v-if="showRetry" @click="handleRetry" class="error-state__btn error-state__btn--primary">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <polyline points="23,4 23,10 17,10" />
                <path d="M20.49 15a9 9 0 1 1-2.12-9.36L23 10" />
              </svg>
              {{ retryText }}
            </button>

            <button v-if="showGoBack" @click="handleGoBack" class="error-state__btn error-state__btn--secondary">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="m15 18-6-6 6-6" />
              </svg>
              {{ goBackText }}
            </button>

            <button v-if="showGoHome" @click="handleGoHome" class="error-state__btn error-state__btn--secondary">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
                <polyline points="9,22 9,12 15,12 15,22" />
              </svg>
              {{ goHomeText }}
            </button>
          </slot>
        </div>
      </div>
    </div>

    <!-- Fullscreen variant -->
    <div v-else class="error-state__content">
      <div class="error-state__icon" v-if="showIcon">
        <svg v-if="type === '404'" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <circle cx="11" cy="11" r="8" />
          <path d="m21 21-4.35-4.35" />
        </svg>
        <svg v-else-if="type === 'network'" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z" />
          <line x1="12" y1="9" x2="12" y2="13" />
          <line x1="12" y1="17" x2="12.01" y2="17" />
        </svg>
        <svg v-else-if="type === 'server'" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <rect x="2" y="3" width="20" height="14" rx="2" ry="2" />
          <line x1="8" y1="21" x2="16" y2="21" />
          <line x1="12" y1="17" x2="12" y2="21" />
          <line x1="7" y1="7" x2="7.01" y2="7" />
          <line x1="12" y1="7" x2="12.01" y2="7" />
          <line x1="17" y1="7" x2="17.01" y2="7" />
        </svg>
        <svg v-else viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <circle cx="12" cy="12" r="10" />
          <line x1="15" y1="9" x2="9" y2="15" />
          <line x1="9" y1="9" x2="15" y2="15" />
        </svg>
      </div>

      <div class="error-state__text">
        <h1 class="error-state__title">{{ title }}</h1>
        <p class="error-state__message">{{ message }}</p>
        <p v-if="details" class="error-state__details">{{ details }}</p>
      </div>

      <div class="error-state__actions" v-if="showActions || $slots.actions">
        <!-- Custom actions slot -->
        <slot name="actions">
          <!-- Default actions -->
          <button v-if="showRetry" @click="handleRetry" class="error-state__btn error-state__btn--primary">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <polyline points="23,4 23,10 17,10" />
              <path d="M20.49 15a9 9 0 1 1-2.12-9.36L23 10" />
            </svg>
            {{ retryText }}
          </button>

          <button v-if="showGoBack" @click="handleGoBack" class="error-state__btn error-state__btn--secondary">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="m15 18-6-6 6-6" />
            </svg>
            {{ goBackText }}
          </button>

          <button v-if="showGoHome" @click="handleGoHome" class="error-state__btn error-state__btn--secondary">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
              <polyline points="9,22 9,12 15,12 15,22" />
            </svg>
            {{ goHomeText }}
          </button>
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  // Content
  title?: string
  message?: string
  details?: string

  // Appearance
  type?: '404' | 'network' | 'server' | 'generic'
  fullscreen?: boolean
  showIcon?: boolean

  // Actions
  showActions?: boolean
  showRetry?: boolean
  showGoBack?: boolean
  showGoHome?: boolean

  // Action text
  retryText?: string
  goBackText?: string
  goHomeText?: string

  // Custom navigation
  backUrl?: string
  homeUrl?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: 'Something went wrong',
  message: 'We encountered an unexpected error. Please try again.',
  type: 'generic',
  fullscreen: false,
  showIcon: true,
  showActions: true,
  showRetry: false,
  showGoBack: true,
  showGoHome: true,
  retryText: 'Try Again',
  goBackText: 'Go Back',
  goHomeText: 'Go Home',
  backUrl: '/browse',
  homeUrl: '/'
})

const emit = defineEmits<{
  retry: []
  goBack: []
  goHome: []
}>()

// Action handlers
const handleRetry = () => {
  emit('retry')
}

const handleGoBack = () => {
  emit('goBack')
  if (props.backUrl) {
    navigateTo(props.backUrl)
  }
}

const handleGoHome = () => {
  emit('goHome')
  navigateTo(props.homeUrl)
}

// Preset configurations for common error types
const getPresetConfig = (type: string) => {
  const presets = {
    '404': {
      title: 'Page Not Found',
      message: "Sorry, we couldn't find the page you're looking for.",
      details: 'The page may have been moved, deleted, or you may have entered an incorrect URL.'
    },
    'network': {
      title: 'Connection Error',
      message: 'Unable to connect to our servers.',
      details: 'Please check your internet connection and try again.'
    },
    'server': {
      title: 'Server Error',
      message: 'Our servers are experiencing issues.',
      details: 'We\'re working to fix this. Please try again in a few minutes.'
    }
  }

  return presets[type] || {}
}

// Apply preset if no custom title/message provided
const preset = getPresetConfig(props.type)
const finalTitle = computed(() => props.title || preset.title || 'Something went wrong')
const finalMessage = computed(() => props.message || preset.message || 'We encountered an unexpected error.')
const finalDetails = computed(() => props.details || preset.details)
</script>

<style lang="scss" scoped>
.error-state {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-xl 0;

  &--fullscreen {
    min-height: 100vh;
    padding: 0;
  }

  &__content {
    text-align: center;
    max-width: 500px;
    width: 100%;
    padding: $spacing-xl;

    @media (max-width: $mobile) {
      padding: $spacing-lg;
    }
  }

  &__icon {
    width: 80px;
    height: 80px;
    margin: 0 auto $spacing-xl auto;
    color: $text-muted;

    svg {
      width: 100%;
      height: 100%;
    }

    @media (max-width: $mobile) {
      width: 60px;
      height: 60px;
      margin-bottom: $spacing-lg;
    }
  }

  &__text {
    margin-bottom: $spacing-xl;

    @media (max-width: $mobile) {
      margin-bottom: $spacing-lg;
    }
  }

  &__title {
    font-family: $font-primary;
    font-size: $font-3xl;
    font-weight: $font-bold;
    color: $text-primary;
    margin: 0 0 $spacing-md 0;

    @media (max-width: $mobile) {
      font-size: $font-2xl;
    }
  }

  &__message {
    font-family: $font-secondary;
    font-size: $font-lg;
    color: $text-secondary;
    margin: 0 0 $spacing-sm 0;
    line-height: $leading-relaxed;

    @media (max-width: $mobile) {
      font-size: $font-base;
    }
  }

  &__details {
    font-family: $font-secondary;
    font-size: $font-sm;
    color: $text-muted;
    margin: 0;
    line-height: $leading-relaxed;
  }

  &__actions {
    display: flex;
    gap: $spacing-md;
    justify-content: center;
    flex-wrap: wrap;

    @media (max-width: $mobile) {
      flex-direction: column;
      align-items: center;
    }
  }

  &__btn {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    padding: $spacing-md $spacing-lg;
    border-radius: $border-radius-lg;
    font-family: $font-secondary;
    font-size: $font-base;
    font-weight: $font-semibold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;

    svg {
      width: 16px;
      height: 16px;
    }

    &--primary {
      background: $primary-orange;
      color: $white;
      border: none;

      &:hover {
        background: color.adjust($primary-orange, $lightness: -10%);
        transform: translateY(-2px);
        box-shadow: $shadow-medium;
      }
    }

    &--secondary {
      background: transparent;
      color: $text-secondary;
      border: 1px solid $border-light;

      &:hover {
        background: $bg-secondary;
        border-color: $primary-orange;
        color: $primary-orange;
      }
    }

    @media (max-width: $mobile) {
      width: 100%;
      max-width: 200px;
      justify-content: center;
    }
  }
}
</style>
