<template>
  <div class="search-bar">
    <div class="search-bar__container">
      <div class="search-bar__main">
        <div class="search-bar__top-row">
          <div class="search-bar__input-group">
            <svg class="search-bar__icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.35-4.35"></path>
            </svg>
            <input v-model="searchQuery" type="text" class="search-bar__input"
              placeholder="Search for dishes, restaurants, or locations..." @keyup.enter="handleSearch" />
          </div>

          <button class="search-bar__button" @click="handleSearch">
            Search Now
          </button>
        </div>

        <div class="search-bar__filters">
          <select v-model="selectedCuisine" class="search-bar__select">
            <option value="">All Cuisines</option>
            <option v-for="cuisine in cuisineTypes" :key="cuisine" :value="cuisine">
              {{ cuisine }}
            </option>
          </select>

          <select v-model="selectedPrice" class="search-bar__select">
            <option value="">Any Price</option>
            <option value="$">$ - Budget</option>
            <option value="$$">$$ - Moderate</option>
            <option value="$$$">$$$ - Expensive</option>
            <option value="$$$$">$$$$ - Fine Dining</option>
          </select>

          <select v-model="selectedRegion" class="search-bar__select">
            <option value="">All Regions</option>
            <option v-for="region in regions" :key="region" :value="region">
              {{ region }}
            </option>
          </select>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  initialQuery?: string
  initialCuisine?: string
  initialPrice?: string
  initialRegion?: string
}

const props = withDefaults(defineProps<Props>(), {
  initialQuery: '',
  initialCuisine: '',
  initialPrice: '',
  initialRegion: ''
})

const searchQuery = ref(props.initialQuery)
const selectedCuisine = ref(props.initialCuisine)
const selectedPrice = ref(props.initialPrice)
const selectedRegion = ref(props.initialRegion)

// Watch for prop changes and update local refs
watch(() => props.initialQuery, (newValue) => {
  searchQuery.value = newValue
})

watch(() => props.initialCuisine, (newValue) => {
  selectedCuisine.value = newValue
})

watch(() => props.initialPrice, (newValue) => {
  selectedPrice.value = newValue
})

watch(() => props.initialRegion, (newValue) => {
  selectedRegion.value = newValue
})

const cuisineTypes = ref([
  'Chinese', 'Japanese', 'Korean', 'Thai', 'Vietnamese',
  'Indian', 'Malaysian', 'Indonesian', 'Filipino', 'Singaporean'
])

const regions = ref([
  'Downtown', 'Chinatown', 'Little Tokyo', 'Koreatown',
  'East Side', 'West Side', 'North District', 'South District'
])

const handleSearch = () => {
  const searchParams = {
    query: searchQuery.value,
    cuisine: selectedCuisine.value,
    price: selectedPrice.value,
    region: selectedRegion.value
  }

  // Create query string from non-empty parameters
  const queryParams = new URLSearchParams()
  Object.entries(searchParams).forEach(([key, value]) => {
    if (value && value.trim() !== '') {
      queryParams.append(key, value)
    }
  })

  // Navigate to browse page with search parameters
  navigateTo(`/browse?${queryParams.toString()}`)
}
</script>

<style lang="scss" scoped>
.search-bar {
  width: 100%;
  max-width: 900px;
  margin: 0 auto;

  &__container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: $border-radius-xl;
    padding: $spacing-lg;
    box-shadow: $shadow-heavy;
    border: 1px solid rgba(255, 255, 255, 0.2);

    @media (max-width: $tablet) {
      padding: $spacing-md;
      border-radius: $border-radius-lg;
    }

    @media (max-width: $mobile) {
      padding: $spacing-md $spacing-sm;
      margin: 0 $spacing-sm;
    }
  }

  &__main {
    display: flex;
    flex-direction: column;
    gap: $spacing-lg;
  }

  &__top-row {
    display: flex;
    gap: $spacing-md;
    align-items: center;

    @media (max-width: $tablet) {
      flex-direction: column;
      gap: $spacing-sm;
    }

    @media (max-width: $mobile) {
      gap: $spacing-sm;
    }
  }

  &__input-group {
    position: relative;
    flex: 1;
    width: 100%;

    @media (max-width: $tablet) {
      width: 100%;
      flex: none;
    }
  }

  &__icon {
    position: absolute;
    left: $spacing-md;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    color: $medium-gray;
    z-index: 1;
  }

  &__input {
    width: 100%;
    padding: $spacing-md $spacing-md $spacing-md 3rem;
    border: 2px solid $border-light;
    border-radius: $border-radius-lg;
    font-family: $font-secondary;
    font-size: $font-base;
    background: $white;
    transition: all 0.3s ease;
    box-sizing: border-box;

    &:focus {
      outline: none;
      border-color: $primary-orange;
      box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
    }

    &::placeholder {
      color: $medium-gray;
    }

    @media (max-width: $tablet) {
      width: 100%;
    }

    @media (max-width: $mobile) {
      padding: $spacing-sm $spacing-sm $spacing-sm 2.5rem;
      font-size: $font-sm;
      width: 100%;
    }
  }

  &__filters {
    display: flex;
    gap: $spacing-md;
    flex-wrap: wrap;
    justify-content: center;

    @media (max-width: $tablet) {
      gap: $spacing-sm;
    }

    @media (max-width: $mobile) {
      flex-direction: column;
    }
  }

  &__select {
    padding: $spacing-md;
    border: 2px solid $border-light;
    border-radius: $border-radius-lg;
    font-family: $font-secondary;
    font-size: $font-base;
    background: $white;
    color: $text-primary;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
    box-sizing: border-box;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right $spacing-sm center;
    background-size: 16px;
    padding-right: 2.5rem;

    &:focus {
      outline: none;
      border-color: $primary-orange;
      box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
    }

    @media (max-width: $tablet) {
      min-width: 100%;
      font-size: $font-base;
      padding: $spacing-md $spacing-xl $spacing-md $spacing-md;
    }

    @media (max-width: $mobile) {
      font-size: $font-base;
      padding: $spacing-sm $spacing-lg $spacing-sm $spacing-sm;
    }

    // Style the dropdown options
    option {
      font-size: $font-base;
      padding: $spacing-sm;
      background: $white;
      color: $text-primary;

      @media (max-width: $tablet) {
        font-size: $font-base;
        padding: $spacing-md;
      }
    }
  }

  &__button {
    background: linear-gradient(135deg, $primary-red 0%, $primary-orange 100%);
    color: $text-white;
    border: none;
    padding: $spacing-md $spacing-xl;
    border-radius: $border-radius-lg;
    font-family: $font-secondary;
    font-size: $font-base;
    font-weight: $font-semibold;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    box-shadow: $shadow-medium;
    height: fit-content;
    min-width: 140px;

    &:hover {
      transform: translateY(-2px);
      box-shadow: $shadow-heavy;
    }

    &:active {
      transform: translateY(0);
    }

    @media (max-width: $tablet) {
      width: 100%;
      min-width: auto;
    }
  }
}
</style>
