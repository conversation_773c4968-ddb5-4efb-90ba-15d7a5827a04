<template>
  <div class="restaurant-sidebar">
    <!-- Quick Actions -->
    <div class="sidebar-card">
      <h3 class="sidebar-card__title">Quick Actions</h3>
      <div class="sidebar-card__content">
        <div class="quick-actions">
          <button class="quick-action-btn quick-action-btn--primary" @click="callRestaurant">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path
                d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z" />
            </svg>
            Call Restaurant
          </button>

          <button class="quick-action-btn" @click="getDirections" v-if="restaurant.coordinates">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" />
              <circle cx="12" cy="10" r="3" />
            </svg>
            Get Directions
          </button>

          <button class="quick-action-btn" @click="toggleFavorite"
            :class="{ 'quick-action-btn--favorite': isFavorite }">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path
                d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z" />
            </svg>
            {{ isFavorite ? 'Remove Favorite' : 'Save Favorite' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Restaurant Info -->
    <div class="sidebar-card">
      <h3 class="sidebar-card__title">Restaurant Info</h3>
      <div class="sidebar-card__content">
        <div class="restaurant-info">
          <!-- Opening Hours -->
          <div class="restaurant-info__item">
            <div class="restaurant-info__icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="12" cy="12" r="10" />
                <polyline points="12,6 12,12 16,14" />
              </svg>
            </div>
            <div class="restaurant-info__content">
              <strong>Hours</strong>
              <div class="hours-info">
                <p class="hours-today" :class="{ 'hours-today--closed': isTodayClosed }">{{ todayHours }}</p>
                <button class="hours-toggle" @click="showAllHours = !showAllHours">
                  {{ showAllHours ? 'Hide' : 'Show all hours' }}
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" :class="{ 'rotated': showAllHours }">
                    <polyline points="6,9 12,15 18,9" />
                  </svg>
                </button>
                <div v-if="showAllHours" class="hours-dropdown">
                  <div v-for="(hours, day) in weeklyHours" :key="day" class="hours-day">
                    <span class="hours-day-name">{{ day }}</span>
                    <span class="hours-day-time" :class="{ 'hours-day-time--closed': isClosedDay(hours) }">{{ hours
                    }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Distance -->
          <div class="restaurant-info__item">
            <div class="restaurant-info__icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" />
                <circle cx="12" cy="10" r="3" />
              </svg>
            </div>
            <div class="restaurant-info__content">
              <strong>Distance</strong>
              <p>{{ calculatedDistance }} away</p>
            </div>
          </div>

          <!-- Website -->
          <div class="restaurant-info__item" v-if="restaurant.businessInfo?.website">
            <div class="restaurant-info__icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="12" cy="12" r="10" />
                <path d="M2 12h20" />
                <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z" />
              </svg>
            </div>
            <div class="restaurant-info__content">
              <strong>Website</strong>
              <p>
                <a :href="restaurant.businessInfo.website" target="_blank" rel="noopener noreferrer"
                  class="website-link">
                  Visit Website
                </a>
              </p>
            </div>
          </div>

          <!-- Estimated Time -->
          <div class="restaurant-info__item" v-if="restaurant.estimatedTime">
            <div class="restaurant-info__icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2" />
                <rect x="8" y="2" width="8" height="4" rx="1" ry="1" />
              </svg>
            </div>
            <div class="restaurant-info__content">
              <strong>Delivery Time</strong>
              <p>{{ restaurant.estimatedTime }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Features -->
    <div class="sidebar-card">
      <h3 class="sidebar-card__title">Features</h3>
      <div class="sidebar-card__content">
        <div class="restaurant-features">
          <div class="feature-item" v-for="feature in restaurantFeatures" :key="feature.name">
            <div class="feature-item__icon" :class="{ 'feature-item__icon--available': feature.available }">
              <svg v-if="feature.available" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <polyline points="20,6 9,17 4,12" />
              </svg>
              <svg v-else viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <line x1="18" y1="6" x2="6" y2="18" />
                <line x1="6" y1="6" x2="18" y2="18" />
              </svg>
            </div>
            <span class="feature-item__name">{{ feature.name }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Similar Restaurants -->
    <div class="sidebar-card">
      <h3 class="sidebar-card__title">Similar Restaurants</h3>
      <div class="sidebar-card__content">
        <div class="similar-restaurants">
          <div v-for="similar in similarRestaurants" :key="similar.id" class="similar-restaurant"
            @click="navigateTo(`/restaurant/${similar.id}`)">
            <div class="similar-restaurant__image">
              <img :src="getSimilarRestaurantImage(similar)" :alt="similar.businessInfo?.name || similar.name" />
            </div>
            <div class="similar-restaurant__content">
              <h4 class="similar-restaurant__name">{{ similar.businessInfo?.name || similar.name }}</h4>
              <div class="similar-restaurant__rating">
                <StarRating :rating="similar.rating" size="small" :show-rating="false" :show-review-count="false" />
                <span class="similar-restaurant__rating-text">{{ similar.rating }}</span>
              </div>
              <p class="similar-restaurant__distance">{{ getRestaurantDistance(similar) }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Restaurant } from '~/types'

interface Props {
  restaurant: Restaurant
}

const props = defineProps<Props>()

const restaurantsStore = useRestaurantsStore()
const { restaurants } = storeToRefs(restaurantsStore)

// Reactive state
const showAllHours = ref(false)
const isFavorite = ref(false)

// Opening hours data from restaurant
const weeklyHours = computed(() => {
  if (!props.restaurant.opening_hours?.length) {
    // Fallback to default hours if no opening_hours data
    return {
      'Monday': '11:00 AM - 10:00 PM',
      'Tuesday': '11:00 AM - 10:00 PM',
      'Wednesday': '11:00 AM - 10:00 PM',
      'Thursday': '11:00 AM - 10:00 PM',
      'Friday': '11:00 AM - 11:00 PM',
      'Saturday': '10:00 AM - 11:00 PM',
      'Sunday': '10:00 AM - 10:00 PM'
    }
  }

  // Convert opening_hours array to object
  const hoursObject: Record<string, string> = {}
  props.restaurant.opening_hours.forEach(hoursString => {
    const day = getDayFromHours(hoursString)
    const time = getTimeFromHours(hoursString)
    if (day && time) {
      hoursObject[day] = time
    }
  })

  return hoursObject
})

const todayHours = computed(() => {
  const today = new Date().toLocaleDateString('en-US', { weekday: 'long' })
  const hours = weeklyHours.value[today as keyof typeof weeklyHours.value]

  if (hours) {
    return hours
  }

  // Fallback: try to find today's hours directly from opening_hours array
  if (props.restaurant.opening_hours?.length) {
    const todayHoursString = props.restaurant.opening_hours.find(hours =>
      hours.toLowerCase().startsWith(today.toLowerCase())
    )
    if (todayHoursString) {
      return getTimeFromHours(todayHoursString)
    }
  }

  return '11:00 AM - 10:00 PM'
})

// Helper functions for parsing opening hours strings
const getDayFromHours = (hoursString: string) => {
  const colonIndex = hoursString.indexOf(':')
  return colonIndex !== -1 ? hoursString.substring(0, colonIndex).trim() : ''
}

const getTimeFromHours = (hoursString: string) => {
  const colonIndex = hoursString.indexOf(':')
  return colonIndex !== -1 ? hoursString.substring(colonIndex + 1).trim() : hoursString
}

const isClosedDay = (hoursString: string) => {
  return hoursString.toLowerCase().includes('closed')
}

const isTodayClosed = computed(() => {
  return todayHours.value?.toLowerCase().includes('closed') || false
})

// Distance calculation using the same function as RestaurantCard
const calculatedDistance = computed(() => {
  if (props.restaurant.distance) {
    return props.restaurant.distance
  }

  // Use the same distance calculation logic as RestaurantCard
  if (props.restaurant.coordinates) {
    // This would use the same geolocation logic
    // For now, return a placeholder
    return '2.5 km'
  }

  return 'Distance unavailable'
})

const restaurantFeatures = computed(() => [
  { name: 'Delivery', available: true },
  { name: 'Takeout', available: true },
  { name: 'Dine-in', available: true },
  { name: 'Outdoor Seating', available: Math.random() > 0.5 },
  { name: 'Wheelchair Accessible', available: Math.random() > 0.3 },
  { name: 'Parking Available', available: Math.random() > 0.4 },
  { name: 'WiFi', available: Math.random() > 0.2 },
  { name: 'Credit Cards', available: true },
  { name: 'Reservations', available: Math.random() > 0.6 },
  { name: 'Kids Friendly', available: Math.random() > 0.4 }
])

const similarRestaurants = computed(() => {
  const currentType = props.restaurant.types?.[0]
  return restaurants.value
    .filter(r =>
      r.id !== props.restaurant.id &&
      r.types?.some(type => type === currentType)
    )
    .slice(0, 3)
})

// Action handlers
const callRestaurant = () => {
  // In a real app, this would open the phone dialer
  alert('Call restaurant functionality would be implemented here')
}

const getDirections = () => {
  if (props.restaurant.coordinates) {
    const { lat, lng } = props.restaurant.coordinates
    const url = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`
    window.open(url, '_blank')
  }
}

const toggleFavorite = () => {
  isFavorite.value = !isFavorite.value
  // In a real app, this would save to favorites store/API
  console.log(`${isFavorite.value ? 'Added to' : 'Removed from'} favorites:`, props.restaurant.businessInfo?.name || props.restaurant.name)
}

// Image helper for similar restaurants (same logic as RestaurantCard)
const getSimilarRestaurantImage = (restaurant: Restaurant) => {
  // Check if restaurant has the new photos structure (RestaurantPhotos[])
  const photos = restaurant.photos as any

  if (photos && Array.isArray(photos)) {
    // Try to find a photo object with isCover property
    const photoWithCover = photos.find((photo: any) =>
      typeof photo === 'object' && photo !== null && photo.isCover === true
    )

    if (photoWithCover && photoWithCover.downloadUrl) {
      return photoWithCover.downloadUrl
    }
  }

  // Fallback to a default image if no cover photo is found
  return 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=400&h=300&fit=crop'
}

// Distance helper for similar restaurants
const getRestaurantDistance = (restaurant: Restaurant) => {
  if (restaurant.distance) {
    return restaurant.distance
  }

  // Use the same distance calculation logic as RestaurantCard
  if (restaurant.coordinates) {
    // This would use the same geolocation logic
    // For now, return a placeholder
    return '2.5 km'
  }

  return 'Distance unavailable'
}

const getPriceDescription = (priceRange: string) => {
  const descriptions: Record<string, string> = {
    '$': 'Budget friendly',
    '$$': 'Moderate',
    '$$$': 'Expensive',
    '$$$$': 'Very expensive'
  }
  return descriptions[priceRange] || 'Moderate'
}
</script>

<style lang="scss" scoped>
.restaurant-sidebar {
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
  position: sticky;
  top: $spacing-lg;
}

.sidebar-card {
  background: $white;
  border-radius: $border-radius-lg;
  border: 1px solid $border-light;
  overflow: hidden;

  &__title {
    font-family: $font-primary;
    font-size: $font-base;
    font-weight: $font-semibold;
    color: $text-primary;
    margin: 0;
    padding: $spacing-md $spacing-lg;
    background: $bg-secondary;
    border-bottom: 1px solid $border-light;
  }

  &__content {
    padding: $spacing-md;
  }
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.quick-action-btn {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  padding: $spacing-md;
  border: 1px solid $border-light;
  background: $white;
  color: $text-primary;
  border-radius: $border-radius-md;
  font-family: $font-secondary;
  font-weight: $font-medium;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;

  svg {
    width: 16px;
    height: 16px;
  }

  &:hover {
    border-color: $primary-orange;
    color: $primary-orange;
  }

  &--primary {
    background: $primary-orange;
    color: $white;
    border-color: $primary-orange;

    &:hover {
      background: color.adjust($primary-orange, $lightness: -10%);
    }
  }

  &--favorite {
    background: $secondary-green;
    color: $white;
    border-color: $secondary-green;

    &:hover {
      background: color.adjust($secondary-green, $lightness: -10%);
    }
  }
}

.restaurant-info {
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;

  &__item {
    display: flex;
    gap: $spacing-md;
  }

  &__icon {
    width: 20px;
    height: 20px;
    color: $primary-orange;
    flex-shrink: 0;
    margin-top: 2px;

    svg {
      width: 100%;
      height: 100%;
    }
  }

  &__content {
    flex: 1;

    strong {
      font-family: $font-secondary;
      font-weight: $font-semibold;
      color: $text-primary;
      display: block;
      margin-bottom: $spacing-xs;
    }

    p {
      font-family: $font-secondary;
      font-size: $font-sm;
      color: $text-secondary;
      margin: 0;
      line-height: $leading-relaxed;
    }
  }
}

.hours-info {
  .hours-today {
    margin: 0 0 $spacing-xs 0;
    font-family: $font-secondary;
    font-size: $font-sm;
    color: $text-secondary;

    &--closed {
      color: $primary-red;
      font-weight: $font-medium;
    }
  }

  .hours-toggle {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    background: none;
    border: none;
    color: $primary-orange;
    font-family: $font-secondary;
    font-size: $font-xs;
    cursor: pointer;
    padding: 0;
    margin-bottom: $spacing-sm;

    svg {
      width: 12px;
      height: 12px;
      transition: transform 0.3s ease;

      &.rotated {
        transform: rotate(180deg);
      }
    }

    &:hover {
      color: color.adjust($primary-orange, $lightness: -10%);
    }
  }

  .hours-dropdown {
    background: $bg-secondary;
    border-radius: $border-radius-md;
    padding: $spacing-sm;
    margin-top: $spacing-xs;
  }

  .hours-day {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: $spacing-xs 0;
    border-bottom: 1px solid $border-light;

    &:last-child {
      border-bottom: none;
    }

    .hours-day-name {
      font-family: $font-secondary;
      font-size: $font-xs;
      font-weight: $font-medium;
      color: $text-primary;
    }

    .hours-day-time {
      font-family: $font-secondary;
      font-size: $font-xs;
      color: $text-secondary;

      &--closed {
        color: $primary-red;
        font-weight: $font-medium;
      }
    }
  }
}

.website-link {
  color: $primary-orange;
  text-decoration: none;
  font-weight: $font-medium;
  transition: color 0.3s ease;

  &:hover {
    color: color.adjust($primary-orange, $lightness: -10%);
    text-decoration: underline;
  }
}

.restaurant-features {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: $spacing-sm;

  &__icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;

    &--available {
      color: $secondary-green;
    }

    &:not(&--available) {
      color: $text-muted;
    }

    svg {
      width: 100%;
      height: 100%;
    }
  }

  &__name {
    font-family: $font-secondary;
    font-size: $font-sm;
    color: $text-primary;
  }
}

.similar-restaurants {
  display: flex;
  flex-direction: column;
  gap: $spacing-md;
}

.similar-restaurant {
  display: flex;
  gap: $spacing-md;
  padding: $spacing-md;
  border-radius: $border-radius-md;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: $bg-secondary;
  }

  &__image {
    width: 60px;
    height: 60px;
    border-radius: $border-radius-md;
    overflow: hidden;
    flex-shrink: 0;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  &__content {
    flex: 1;
    min-width: 0;
  }

  &__name {
    font-family: $font-primary;
    font-size: $font-sm;
    font-weight: $font-semibold;
    color: $text-primary;
    margin: 0 0 $spacing-xs 0;
    line-height: $leading-tight;
  }

  &__rating {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    margin-bottom: $spacing-xs;
  }

  &__rating-text {
    font-family: $font-secondary;
    font-size: $font-xs;
    color: $text-secondary;
  }

  &__distance {
    font-family: $font-secondary;
    font-size: $font-xs;
    color: $text-muted;
    margin: 0;
  }
}

@media (max-width: $tablet) {
  .restaurant-sidebar {
    order: -1;
  }
}
</style>
