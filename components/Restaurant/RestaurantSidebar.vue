<template>
  <div class="restaurant-sidebar">
    <!-- Quick Actions -->
    <div class="sidebar-card">
      <h3 class="sidebar-card__title">Quick Actions</h3>
      <div class="sidebar-card__content">
        <div class="quick-actions">
          <button class="quick-action-btn quick-action-btn--primary">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path
                d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z" />
            </svg>
            Call Restaurant
          </button>

          <button class="quick-action-btn">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" />
              <circle cx="12" cy="10" r="3" />
            </svg>
            Get Directions
          </button>

          <button class="quick-action-btn">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path
                d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z" />
            </svg>
            Save Favorite
          </button>
        </div>
      </div>
    </div>

    <!-- Restaurant Info -->
    <div class="sidebar-card">
      <h3 class="sidebar-card__title">Restaurant Info</h3>
      <div class="sidebar-card__content">
        <div class="restaurant-info">
          <div class="restaurant-info__item">
            <div class="restaurant-info__icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="12" cy="12" r="10" />
                <polyline points="12,6 12,12 16,14" />
              </svg>
            </div>
            <div class="restaurant-info__content">
              <strong>Hours</strong>
              <p>{{ restaurant.hours || 'Mon-Sun: 11:00 AM - 10:00 PM' }}</p>
            </div>
          </div>

          <div class="restaurant-info__item">
            <div class="restaurant-info__icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
              </svg>
            </div>
            <div class="restaurant-info__content">
              <strong>Price Range</strong>
              <p>{{ restaurant.priceRange || '$$' }} - {{ getPriceDescription(restaurant.priceRange) }}</p>
            </div>
          </div>

          <div class="restaurant-info__item">
            <div class="restaurant-info__icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" />
                <circle cx="12" cy="10" r="3" />
              </svg>
            </div>
            <div class="restaurant-info__content">
              <strong>Distance</strong>
              <p>{{ restaurant.distance || '2.5 km' }} away</p>
            </div>
          </div>

          <div class="restaurant-info__item">
            <div class="restaurant-info__icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2" />
                <rect x="8" y="2" width="8" height="4" rx="1" ry="1" />
              </svg>
            </div>
            <div class="restaurant-info__content">
              <strong>Delivery Time</strong>
              <p>{{ restaurant.estimatedTime || '25-35 min' }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Features -->
    <div class="sidebar-card">
      <h3 class="sidebar-card__title">Features</h3>
      <div class="sidebar-card__content">
        <div class="restaurant-features">
          <div class="feature-item" v-for="feature in restaurantFeatures" :key="feature.name">
            <div class="feature-item__icon" :class="{ 'feature-item__icon--available': feature.available }">
              <svg v-if="feature.available" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <polyline points="20,6 9,17 4,12" />
              </svg>
              <svg v-else viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <line x1="18" y1="6" x2="6" y2="18" />
                <line x1="6" y1="6" x2="18" y2="18" />
              </svg>
            </div>
            <span class="feature-item__name">{{ feature.name }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Similar Restaurants -->
    <div class="sidebar-card">
      <h3 class="sidebar-card__title">Similar Restaurants</h3>
      <div class="sidebar-card__content">
        <div class="similar-restaurants">
          <div v-for="similar in similarRestaurants" :key="similar.id" class="similar-restaurant"
            @click="navigateTo(`/restaurant/${similar.id}`)">
            <div class="similar-restaurant__image">
              <img :src="similar.image || '/images/placeholder-restaurant.jpg'" :alt="similar.name" />
            </div>
            <div class="similar-restaurant__content">
              <h4 class="similar-restaurant__name">{{ similar.name }}</h4>
              <div class="similar-restaurant__rating">
                <StarRating :rating="similar.rating" size="small" :show-rating="false" :show-review-count="false" />
                <span class="similar-restaurant__rating-text">{{ similar.rating }}</span>
              </div>
              <p class="similar-restaurant__distance">{{ similar.distance }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  restaurant: any
}

const props = defineProps<Props>()

const restaurantsStore = useRestaurantsStore()
const { restaurants } = storeToRefs(restaurantsStore)

const restaurantFeatures = computed(() => [
  { name: 'Delivery', available: true },
  { name: 'Takeout', available: true },
  { name: 'Dine-in', available: true },
  { name: 'Outdoor Seating', available: Math.random() > 0.5 },
  { name: 'Wheelchair Accessible', available: Math.random() > 0.3 },
  { name: 'Parking Available', available: Math.random() > 0.4 },
  { name: 'WiFi', available: Math.random() > 0.2 },
  { name: 'Credit Cards', available: true },
  { name: 'Reservations', available: Math.random() > 0.6 },
  { name: 'Kids Friendly', available: Math.random() > 0.4 }
])

const similarRestaurants = computed(() => {
  const currentType = props.restaurant.types?.[0]
  return restaurants.value
    .filter(r =>
      r.id !== props.restaurant.id &&
      r.types?.some(type => type === currentType)
    )
    .slice(0, 3)
})

const getPriceDescription = (priceRange: string) => {
  const descriptions = {
    '$': 'Budget friendly',
    '$$': 'Moderate',
    '$$$': 'Expensive',
    '$$$$': 'Very expensive'
  }
  return descriptions[priceRange] || 'Moderate'
}
</script>

<style lang="scss" scoped>
.restaurant-sidebar {
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
  position: sticky;
  top: $spacing-lg;
}

.sidebar-card {
  background: $white;
  border-radius: $border-radius-lg;
  border: 1px solid $border-light;
  overflow: hidden;

  &__title {
    font-family: $font-primary;
    font-size: $font-base;
    font-weight: $font-semibold;
    color: $text-primary;
    margin: 0;
    padding: $spacing-md $spacing-lg;
    background: $bg-secondary;
    border-bottom: 1px solid $border-light;
  }

  &__content {
    padding: $spacing-md;
  }
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.quick-action-btn {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  padding: $spacing-md;
  border: 1px solid $border-light;
  background: $white;
  color: $text-primary;
  border-radius: $border-radius-md;
  font-family: $font-secondary;
  font-weight: $font-medium;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;

  svg {
    width: 16px;
    height: 16px;
  }

  &:hover {
    border-color: $primary-orange;
    color: $primary-orange;
  }

  &--primary {
    background: $primary-orange;
    color: $white;
    border-color: $primary-orange;

    &:hover {
      background: color.adjust($primary-orange, $lightness: -10%);
    }
  }
}

.restaurant-info {
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;

  &__item {
    display: flex;
    gap: $spacing-md;
  }

  &__icon {
    width: 20px;
    height: 20px;
    color: $primary-orange;
    flex-shrink: 0;
    margin-top: 2px;

    svg {
      width: 100%;
      height: 100%;
    }
  }

  &__content {
    flex: 1;

    strong {
      font-family: $font-secondary;
      font-weight: $font-semibold;
      color: $text-primary;
      display: block;
      margin-bottom: $spacing-xs;
    }

    p {
      font-family: $font-secondary;
      font-size: $font-sm;
      color: $text-secondary;
      margin: 0;
      line-height: $leading-relaxed;
    }
  }
}

.restaurant-features {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: $spacing-sm;

  &__icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;

    &--available {
      color: $secondary-green;
    }

    &:not(&--available) {
      color: $text-muted;
    }

    svg {
      width: 100%;
      height: 100%;
    }
  }

  &__name {
    font-family: $font-secondary;
    font-size: $font-sm;
    color: $text-primary;
  }
}

.similar-restaurants {
  display: flex;
  flex-direction: column;
  gap: $spacing-md;
}

.similar-restaurant {
  display: flex;
  gap: $spacing-md;
  padding: $spacing-md;
  border-radius: $border-radius-md;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: $bg-secondary;
  }

  &__image {
    width: 60px;
    height: 60px;
    border-radius: $border-radius-md;
    overflow: hidden;
    flex-shrink: 0;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  &__content {
    flex: 1;
    min-width: 0;
  }

  &__name {
    font-family: $font-primary;
    font-size: $font-sm;
    font-weight: $font-semibold;
    color: $text-primary;
    margin: 0 0 $spacing-xs 0;
    line-height: $leading-tight;
  }

  &__rating {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    margin-bottom: $spacing-xs;
  }

  &__rating-text {
    font-family: $font-secondary;
    font-size: $font-xs;
    color: $text-secondary;
  }

  &__distance {
    font-family: $font-secondary;
    font-size: $font-xs;
    color: $text-muted;
    margin: 0;
  }
}

@media (max-width: $tablet) {
  .restaurant-sidebar {
    order: -1;
  }
}
</style>
