<template>
  <div class="restaurant-menu">
    <!-- <PERSON><PERSON> -->
    <div class="restaurant-menu__header">
      <!-- Menu Categories -->
      <div class="restaurant-menu__categories">
        <button v-for="category in menuCategories" :key="category.id" class="restaurant-menu__category-btn"
          :class="{ 'restaurant-menu__category-btn--active': activeCategory === category.id }"
          @click="activeCategory = category.id">
          {{ category.name }}
        </button>
      </div>

      <!-- View Toggle -->
      <div class="restaurant-menu__view-toggle">
        <button class="restaurant-menu__view-btn" :class="{ 'restaurant-menu__view-btn--active': viewMode === 'list' }"
          @click="viewMode = 'list'" title="List View">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <line x1="8" y1="6" x2="21" y2="6" />
            <line x1="8" y1="12" x2="21" y2="12" />
            <line x1="8" y1="18" x2="21" y2="18" />
            <line x1="3" y1="6" x2="3.01" y2="6" />
            <line x1="3" y1="12" x2="3.01" y2="12" />
            <line x1="3" y1="18" x2="3.01" y2="18" />
          </svg>
        </button>
        <button class="restaurant-menu__view-btn" :class="{ 'restaurant-menu__view-btn--active': viewMode === 'grid' }"
          @click="viewMode = 'grid'" title="Grid View">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <rect x="3" y="3" width="7" height="7" />
            <rect x="14" y="3" width="7" height="7" />
            <rect x="14" y="14" width="7" height="7" />
            <rect x="3" y="14" width="7" height="7" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Menu Items -->
    <div class="restaurant-menu__items">
      <div v-for="category in filteredCategories" :key="category.id" class="restaurant-menu__category">
        <h3 class="restaurant-menu__category-title">{{ category.name }}</h3>

        <!-- Grid View -->
        <div v-if="viewMode === 'grid'" class="restaurant-menu__category-items restaurant-menu__category-items--grid">
          <div v-for="item in category.items" :key="item.id" class="menu-item menu-item--grid">
            <div class="menu-item__image">
              <img :src="getMenuItemImage(item, category.id)" :alt="item.name" />
            </div>

            <div class="menu-item__content">
              <div class="menu-item__header">
                <h4 class="menu-item__name">{{ item.name }}</h4>
                <span class="menu-item__price">${{ item.price }}</span>
              </div>

              <p class="menu-item__description">{{ item.description }}</p>

              <div class="menu-item__tags" v-if="item.tags?.length">
                <span v-for="tag in item.tags" :key="tag" class="menu-item__tag"
                  :class="`menu-item__tag--${tag.toLowerCase()}`">
                  {{ tag }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- List View -->
        <div v-else class="restaurant-menu__category-items restaurant-menu__category-items--list">
          <div v-for="item in category.items" :key="item.id" class="menu-item menu-item--list">
            <div class="menu-item__image">
              <img :src="getMenuItemImage(item, category.id)" :alt="item.name" />
            </div>

            <div class="menu-item__content">
              <div class="menu-item__header">
                <h4 class="menu-item__name">{{ item.name }}</h4>
                <span class="menu-item__price">${{ item.price }}</span>
              </div>

              <p class="menu-item__description">{{ item.description }}</p>

              <div class="menu-item__tags" v-if="item.tags?.length">
                <span v-for="tag in item.tags" :key="tag" class="menu-item__tag"
                  :class="`menu-item__tag--${tag.toLowerCase()}`">
                  {{ tag }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  restaurant: any
}

const props = defineProps<Props>()

const activeCategory = ref('all')
const viewMode = ref('grid')

// Menu categories
const menuCategories = computed(() => {
  const baseCategories = [
    { id: 'all', name: 'All Items' },
    { id: 'appetizers', name: 'Appetizers' },
    { id: 'mains', name: 'Main Dishes' },
    { id: 'desserts', name: 'Desserts' },
    { id: 'beverages', name: 'Beverages' }
  ]

  return baseCategories
})

// Generate sample menu items based on cuisine type
const generateMenuItems = () => {
  const cuisineType = props.restaurant.types?.[0]?.toLowerCase() || 'asian'

  const menuTemplates = {
    japanese: {
      appetizers: [
        { name: 'Edamame', description: 'Steamed young soybeans with sea salt', price: '8.50', tags: ['Vegan', 'Gluten-Free'] },
        { name: 'Gyoza', description: 'Pan-fried pork dumplings with ponzu sauce', price: '12.90', tags: ['Popular'] },
        { name: 'Agedashi Tofu', description: 'Lightly fried tofu in savory dashi broth', price: '10.50', tags: ['Vegetarian'] }
      ],
      mains: [
        { name: 'Chicken Teriyaki', description: 'Grilled chicken glazed with teriyaki sauce, served with rice', price: '24.90', tags: ['Popular'] },
        { name: 'Salmon Sashimi', description: 'Fresh salmon slices, 8 pieces', price: '28.50', tags: ['Fresh', 'Gluten-Free'] },
        { name: 'Beef Ramen', description: 'Rich tonkotsu broth with tender beef and fresh noodles', price: '22.90', tags: ['Signature'] }
      ],
      desserts: [
        { name: 'Mochi Ice Cream', description: 'Traditional rice cake filled with ice cream', price: '8.90', tags: ['Popular'] },
        { name: 'Dorayaki', description: 'Pancake sandwich filled with sweet red bean paste', price: '6.50', tags: ['Traditional'] }
      ],
      beverages: [
        { name: 'Green Tea', description: 'Premium sencha green tea', price: '4.50', tags: ['Hot'] },
        { name: 'Sake', description: 'Premium Japanese rice wine', price: '12.00', tags: ['Alcoholic'] }
      ]
    },
    chinese: {
      appetizers: [
        { name: 'Spring Rolls', description: 'Crispy vegetable spring rolls with sweet chili sauce', price: '9.90', tags: ['Vegetarian'] },
        { name: 'Dim Sum Platter', description: 'Assorted steamed dumplings (6 pieces)', price: '16.90', tags: ['Popular'] },
        { name: 'Prawn Crackers', description: 'Light and crispy prawn crackers', price: '6.50', tags: ['Gluten-Free'] }
      ],
      mains: [
        { name: 'Sweet & Sour Pork', description: 'Crispy pork with pineapple and bell peppers', price: '26.90', tags: ['Popular'] },
        { name: 'Kung Pao Chicken', description: 'Spicy chicken with peanuts and vegetables', price: '24.50', tags: ['Spicy'] },
        { name: 'Beef Black Bean', description: 'Tender beef stir-fried with black bean sauce', price: '28.90', tags: ['Signature'] }
      ],
      desserts: [
        { name: 'Fried Ice Cream', description: 'Vanilla ice cream in crispy coating', price: '9.50', tags: ['Popular'] },
        { name: 'Sesame Balls', description: 'Deep-fried glutinous rice balls with red bean', price: '7.90', tags: ['Traditional'] }
      ],
      beverages: [
        { name: 'Jasmine Tea', description: 'Fragrant jasmine tea', price: '4.00', tags: ['Hot'] },
        { name: 'Chinese Beer', description: 'Tsingtao beer', price: '8.50', tags: ['Alcoholic'] }
      ]
    },
    korean: {
      appetizers: [
        { name: 'Kimchi', description: 'Fermented spicy cabbage', price: '8.00', tags: ['Spicy', 'Vegan'] },
        { name: 'Korean Fried Chicken', description: 'Crispy chicken wings with gochujang glaze', price: '15.90', tags: ['Popular', 'Spicy'] },
        { name: 'Japchae', description: 'Sweet potato noodles with vegetables', price: '12.50', tags: ['Vegetarian'] }
      ],
      mains: [
        { name: 'Bulgogi', description: 'Marinated beef BBQ with rice and banchan', price: '29.90', tags: ['Signature'] },
        { name: 'Bibimbap', description: 'Mixed rice bowl with vegetables and fried egg', price: '22.90', tags: ['Popular'] },
        { name: 'Korean BBQ Pork', description: 'Grilled pork belly with lettuce wraps', price: '32.90', tags: ['BBQ'] }
      ],
      desserts: [
        { name: 'Bingsu', description: 'Shaved ice with sweet toppings', price: '12.90', tags: ['Popular'] },
        { name: 'Hotteok', description: 'Sweet pancake filled with brown sugar', price: '8.50', tags: ['Traditional'] }
      ],
      beverages: [
        { name: 'Korean Tea', description: 'Traditional barley tea', price: '4.50', tags: ['Hot'] },
        { name: 'Soju', description: 'Korean rice wine', price: '15.00', tags: ['Alcoholic'] }
      ]
    }
  }

  const template = menuTemplates[cuisineType as keyof typeof menuTemplates] || menuTemplates.japanese

  return Object.entries(template).map(([categoryId, items]) => ({
    id: categoryId,
    name: categoryId.charAt(0).toUpperCase() + categoryId.slice(1),
    items: (items as any[]).map((item: any, index: number) => ({
      id: `${categoryId}-${index}`,
      ...item
    }))
  }))
}

const menuItems = ref(generateMenuItems())

const filteredCategories = computed(() => {
  if (activeCategory.value === 'all') {
    return menuItems.value
  }
  return menuItems.value.filter(category => category.id === activeCategory.value)
})

// Generate menu item images based on cuisine and category
const getMenuItemImage = (item: any, categoryId: string) => {
  const cuisineType = props.restaurant.types?.[0]?.toLowerCase() || 'asian'

  // Sample food images from Unsplash
  const imageMap = {
    japanese: {
      appetizers: [
        'https://images.unsplash.com/photo-1553621042-f6e147245754?w=400&h=300&fit=crop', // Edamame
        'https://images.unsplash.com/photo-1496116218417-1a781b1c416c?w=400&h=300&fit=crop', // Gyoza
        'https://images.unsplash.com/photo-1706468238718-bba7e9b63ad2?w=400&h=300&fit=crop'  // Tofu
      ],
      mains: [
        'https://images.unsplash.com/photo-1546833999-b9f581a1996d?w=400&h=300&fit=crop', // Teriyaki
        'https://images.unsplash.com/photo-1579584425555-c3ce17fd4351?w=400&h=300&fit=crop', // Sashimi
        'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?w=400&h=300&fit=crop'  // Ramen
      ],
      desserts: [
        'https://images.unsplash.com/photo-1563805042-7684c019e1cb?w=400&h=300&fit=crop', // Mochi
        'https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=400&h=300&fit=crop'  // Dorayaki
      ],
      beverages: [
        'https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=400&h=300&fit=crop', // Tea
        'https://images.unsplash.com/photo-1551538827-9c037cb4f32a?w=400&h=300&fit=crop'  // Sake
      ]
    },
    chinese: {
      appetizers: [
        'https://images.unsplash.com/photo-1563379091339-03246963d96c?w=400&h=300&fit=crop', // Spring rolls
        'https://images.unsplash.com/photo-1496116218417-1a781b1c416c?w=400&h=300&fit=crop', // Dim sum
        'https://images.unsplash.com/photo-1582878826629-29b7ad1cdc43?w=400&h=300&fit=crop'  // Crackers
      ],
      mains: [
        'https://images.unsplash.com/photo-1559847844-d721426d6edc?w=400&h=300&fit=crop', // Sweet & sour
        'https://images.unsplash.com/photo-1596797038530-2c107229654b?w=400&h=300&fit=crop', // Kung pao
        'https://images.unsplash.com/photo-1603133872878-684f208fb84b?w=400&h=300&fit=crop'  // Beef
      ],
      desserts: [
        'https://images.unsplash.com/photo-1563805042-7684c019e1cb?w=400&h=300&fit=crop', // Ice cream
        'https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=400&h=300&fit=crop'  // Sesame balls
      ],
      beverages: [
        'https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=400&h=300&fit=crop', // Tea
        'https://images.unsplash.com/photo-1608270586620-248524c67de9?w=400&h=300&fit=crop'  // Beer
      ]
    },
    korean: {
      appetizers: [
        'https://images.unsplash.com/photo-1567620832903-9fc6debc209f?w=400&h=300&fit=crop', // Kimchi
        'https://images.unsplash.com/photo-1562967914-608f82629710?w=400&h=300&fit=crop', // Fried chicken
        'https://images.unsplash.com/photo-1582878826629-29b7ad1cdc43?w=400&h=300&fit=crop'  // Japchae
      ],
      mains: [
        'https://images.unsplash.com/photo-1590301157890-4810ed352733?w=400&h=300&fit=crop', // Bulgogi
        'https://images.unsplash.com/photo-1553909489-cd47e0ef937f?w=400&h=300&fit=crop', // Bibimbap
        'https://images.unsplash.com/photo-1590301157890-4810ed352733?w=400&h=300&fit=crop'  // BBQ
      ],
      desserts: [
        'https://images.unsplash.com/photo-1563805042-7684c019e1cb?w=400&h=300&fit=crop', // Bingsu
        'https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=400&h=300&fit=crop'  // Hotteok
      ],
      beverages: [
        'https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=400&h=300&fit=crop', // Tea
        'https://images.unsplash.com/photo-1551538827-9c037cb4f32a?w=400&h=300&fit=crop'  // Soju
      ]
    }
  }

  const cuisineImages = imageMap[cuisineType as keyof typeof imageMap] || imageMap.japanese
  const categoryImages = cuisineImages[categoryId as keyof typeof cuisineImages] || cuisineImages.mains

  // Get image based on item index in category
  const itemIndex = menuItems.value
    .find(cat => cat.id === categoryId)?.items
    .findIndex(i => i.id === item.id) || 0

  return categoryImages[itemIndex % categoryImages.length]
}
</script>

<style lang="scss" scoped>
.restaurant-menu {
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: $spacing-xl;
    gap: $spacing-lg;

    @media (max-width: $tablet) {
      flex-direction: column;
      align-items: stretch;
      gap: $spacing-md;
    }
  }

  &__categories {
    display: flex;
    gap: $spacing-sm;
    overflow-x: auto;
    padding-bottom: $spacing-sm;
    flex: 1;

    @media (max-width: $mobile) {
      gap: $spacing-xs;
    }
  }

  &__view-toggle {
    display: flex;
    border: 1px solid $border-light;
    border-radius: $border-radius-md;
    overflow: hidden;
    flex-shrink: 0;

    @media (max-width: $tablet) {
      align-self: flex-end;
    }
  }

  &__view-btn {
    padding: $spacing-sm;
    background: $white;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    svg {
      width: 16px;
      height: 16px;
      color: $text-secondary;
    }

    &:hover {
      background: $bg-secondary;
    }

    &--active {
      background: $primary-orange;

      svg {
        color: $white;
      }
    }
  }

  &__category-btn {
    padding: $spacing-sm $spacing-lg;
    border: 1px solid $border-light;
    background: $white;
    color: $text-secondary;
    border-radius: $border-radius-lg;
    font-family: $font-secondary;
    font-size: $font-sm;
    font-weight: $font-medium;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;

    &:hover {
      border-color: $primary-orange;
      color: $primary-orange;
    }

    &--active {
      background: $primary-orange;
      color: $white;
      border-color: $primary-orange;
    }
  }

  &__category {
    margin-bottom: $spacing-xl;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__category-title {
    font-family: $font-primary;
    font-size: $font-xl;
    font-weight: $font-bold;
    color: $text-primary;
    margin-bottom: $spacing-md;
    padding-bottom: $spacing-xs;
    border-bottom: 2px solid $primary-orange;
    display: flex;
    align-items: center;
    gap: $spacing-sm;

    &::before {
      content: '';
      width: 3px;
      height: 20px;
      background: $primary-orange;
      border-radius: 2px;
    }
  }

  &__category-items {
    &--list {
      display: flex;
      flex-direction: column;
      gap: $spacing-sm;
    }

    &--grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: $spacing-lg;

      @media (max-width: $tablet) {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: $spacing-md;
      }

      @media (max-width: $mobile) {
        grid-template-columns: 1fr;
      }
    }
  }
}

.menu-item {
  background: $white;
  border-radius: $border-radius-md;
  border: 1px solid $border-light;
  transition: all 0.3s ease;
  overflow: hidden;

  &:hover {
    box-shadow: $shadow-light;
    border-color: $primary-orange;
  }

  &--list {
    display: flex;
    gap: $spacing-sm;
    padding: $spacing-sm $spacing-md;

    @media (max-width: $tablet) {
      flex-direction: column;
      gap: $spacing-xs;
      padding: $spacing-sm;
    }

    .menu-item__image {
      width: 60px;
      height: 60px;
      flex-shrink: 0;
      border-radius: $border-radius-sm;
      overflow: hidden;

      @media (max-width: $tablet) {
        width: 100%;
        height: 120px;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .menu-item__content {
      flex: 1;
      min-width: 0;
    }

    .menu-item__header {
      margin-bottom: $spacing-xs;
    }

    .menu-item__name {
      font-size: $font-base;
      margin: 0;
      line-height: $leading-tight;
    }

    .menu-item__price {
      font-size: $font-base;
      font-weight: $font-bold;
    }

    .menu-item__description {
      font-size: $font-sm;
      line-height: $leading-relaxed;
      margin: 0 0 $spacing-xs 0;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .menu-item__tags {
      margin: 0;
    }

    .menu-item__tag {
      font-size: $font-xs;
      padding: 2px $spacing-xs;
    }
  }

  &--grid {
    display: flex;
    flex-direction: column;

    .menu-item__image {
      width: 100%;
      height: 200px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }

      &:hover img {
        transform: scale(1.05);
      }
    }

    .menu-item__content {
      padding: $spacing-md;
    }

    .menu-item__header {
      flex-direction: column;
      align-items: flex-start;
      gap: $spacing-xs;
      margin-bottom: $spacing-sm;
    }

    .menu-item__price {
      font-size: $font-lg;
      font-weight: $font-bold;
    }

    .menu-item__description {
      display: -webkit-box;
      -webkit-line-clamp: 3;
      line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
      margin-bottom: $spacing-sm;
    }
  }

  &__content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: $spacing-sm;
    gap: $spacing-md;
  }

  &__name {
    font-family: $font-primary;
    font-size: $font-lg;
    font-weight: $font-semibold;
    color: $text-primary;
    margin: 0;
  }

  &__price {
    font-family: $font-secondary;
    font-size: $font-lg;
    font-weight: $font-bold;
    color: $primary-orange;
    white-space: nowrap;
  }

  &__description {
    font-family: $font-secondary;
    font-size: $font-sm;
    color: $text-secondary;
    line-height: $leading-relaxed;
    margin: 0 0 $spacing-md 0;
  }

  &__tags {
    display: flex;
    gap: $spacing-xs;
    margin-bottom: $spacing-md;
    flex-wrap: wrap;
  }

  &__tag {
    padding: $spacing-xs $spacing-sm;
    border-radius: $border-radius-sm;
    font-family: $font-secondary;
    font-size: $font-xs;
    font-weight: $font-medium;
    background: $bg-secondary;
    color: $text-secondary;

    &--popular {
      background: rgba(255, 107, 53, 0.1);
      color: $primary-orange;
    }

    &--spicy {
      background: rgba(244, 67, 54, 0.1);
      color: #f44336;
    }

    &--vegetarian,
    &--vegan {
      background: rgba(76, 175, 80, 0.1);
      color: $secondary-green;
    }

    &--signature {
      background: rgba(156, 39, 176, 0.1);
      color: #9c27b0;
    }
  }


}
</style>
