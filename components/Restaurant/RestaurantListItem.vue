<template>
  <div class="restaurant-list-item" @click="$emit('click', restaurant)">
    <div class="restaurant-list-item__image">
      <img :src="coverImageUrl" :alt="restaurant.name" class="restaurant-list-item__img" />
      <div class="restaurant-list-item__rating">
        <StarRating :rating="restaurant.rating" size="small" :show-rating="false" :show-review-count="false" />
        <span class="restaurant-list-item__rating-value">{{ restaurant.rating }}</span>
      </div>
    </div>

    <div class="restaurant-list-item__content">
      <div class="restaurant-list-item__header">
        <h3 class="restaurant-list-item__name">{{ restaurant.name }}</h3>
        <div class="restaurant-list-item__badges">
          <span v-for="type in restaurant.types?.slice(0, 2)" :key="type" class="restaurant-list-item__badge">
            {{ type }}
          </span>
        </div>
      </div>

      <p class="restaurant-list-item__address">
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" />
          <circle cx="12" cy="10" r="3" />
        </svg>
        {{ restaurant.address }}
      </p>

      <div class="restaurant-list-item__details">
        <div class="restaurant-list-item__distance" v-if="restaurant.distance">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="12" cy="12" r="10" />
            <path d="M12 6v6l4 2" />
          </svg>
          {{ restaurant.distance }}
        </div>

        <div class="restaurant-list-item__time" v-if="restaurant.estimatedTime">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="12" cy="12" r="10" />
            <polyline points="12,6 12,12 16,14" />
          </svg>
          {{ restaurant.estimatedTime }}
        </div>

        <div class="restaurant-list-item__price" v-if="restaurant.priceRange">
          <span class="restaurant-list-item__price-symbol">{{ restaurant.priceRange }}</span>
        </div>
      </div>
    </div>

    <div class="restaurant-list-item__actions">
      <button class="restaurant-list-item__btn restaurant-list-item__btn--primary" @click.stop="viewMenu">
        View Menu
      </button>
      <button class="restaurant-list-item__btn restaurant-list-item__btn--secondary">
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path
            d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z" />
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  restaurant: any
}

const props = defineProps<Props>()
defineEmits(['click'])

// Get cover image from photos array
const coverImageUrl = computed(() => {
  // Check if restaurant has the new photos structure (RestaurantPhotos[])
  const photos = props.restaurant.photos as any

  if (photos && Array.isArray(photos)) {
    // Try to find a photo object with isCover property
    const photoWithCover = photos.find((photo: any) =>
      typeof photo === 'object' && photo !== null && photo.isCover === true
    )

    if (photoWithCover && photoWithCover.downloadUrl) {
      return photoWithCover.downloadUrl
    }
  }

  // Fallback to a default image if no cover photo is found
  return 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=400&h=300&fit=crop'
})

const viewMenu = () => {
  navigateTo(`/restaurant/${props.restaurant.id}`)
}
</script>

<style lang="scss" scoped>
.restaurant-list-item {
  display: flex;
  align-items: center;
  gap: $spacing-lg;
  background: $white;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  box-shadow: $shadow-light;
  border: 1px solid $border-light;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: $shadow-medium;
    border-color: $primary-orange;
  }

  @media (max-width: $tablet) {
    flex-direction: column;
    text-align: center;
  }

  &__image {
    position: relative;
    flex-shrink: 0;
    width: 120px;
    height: 120px;

    @media (max-width: $tablet) {
      width: 100%;
      height: 200px;
    }
  }

  &__img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: $border-radius-md;
  }

  &__rating {
    position: absolute;
    top: $spacing-sm;
    right: $spacing-sm;
    background: rgba(0, 0, 0, 0.8);
    color: $white;
    padding: $spacing-xs $spacing-sm;
    border-radius: $border-radius-sm;
    font-size: $font-xs;
    font-weight: $font-semibold;
    display: flex;
    align-items: center;
    gap: $spacing-xs;
  }

  &__rating-value {
    color: $white;
    font-size: $font-xs;
    font-weight: $font-semibold;
  }

  &__content {
    flex: 1;
    min-width: 0;
  }

  &__header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: $spacing-md;
    margin-bottom: $spacing-sm;

    @media (max-width: $tablet) {
      flex-direction: column;
      align-items: center;
      text-align: center;
    }
  }

  &__name {
    font-family: $font-primary;
    font-size: $font-xl;
    font-weight: $font-bold;
    color: $text-primary;
    margin: 0;
    line-height: $leading-tight;
  }

  &__badges {
    display: flex;
    gap: $spacing-xs;
    flex-wrap: wrap;
  }

  &__badge {
    background: $bg-secondary;
    color: $text-secondary;
    padding: $spacing-xs $spacing-sm;
    border-radius: $border-radius-sm;
    font-size: $font-xs;
    font-weight: $font-medium;
    text-transform: capitalize;
  }

  &__address {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    font-family: $font-secondary;
    font-size: $font-sm;
    color: $text-secondary;
    margin: 0 0 $spacing-md 0;
    line-height: $leading-relaxed;

    svg {
      width: 14px;
      height: 14px;
      flex-shrink: 0;
    }

    @media (max-width: $tablet) {
      justify-content: center;
    }
  }

  &__details {
    display: flex;
    align-items: center;
    gap: $spacing-lg;
    font-family: $font-secondary;
    font-size: $font-sm;

    @media (max-width: $tablet) {
      justify-content: center;
    }

    @media (max-width: $mobile) {
      flex-direction: column;
      gap: $spacing-sm;
    }
  }

  &__distance,
  &__time {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    color: $text-secondary;

    svg {
      width: 14px;
      height: 14px;
    }
  }

  &__time {
    color: $primary-orange;
    font-weight: $font-semibold;
  }

  &__price {
    display: flex;
    align-items: center;
  }

  &__price-symbol {
    font-weight: $font-bold;
    color: $secondary-green;
    font-size: $font-base;
  }

  &__actions {
    display: flex;
    flex-direction: column;
    gap: $spacing-sm;
    flex-shrink: 0;

    @media (max-width: $tablet) {
      flex-direction: row;
      justify-content: center;
    }
  }

  &__btn {
    padding: $spacing-sm $spacing-md;
    border-radius: $border-radius-md;
    font-family: $font-secondary;
    font-size: $font-sm;
    font-weight: $font-semibold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: $spacing-xs;

    &--primary {
      background: $primary-orange;
      color: $white;
      border: none;

      &:hover {
        background: color.adjust($primary-orange, $lightness: -10%);
      }
    }

    &--secondary {
      background: transparent;
      color: $text-secondary;
      border: 1px solid $border-light;
      padding: $spacing-sm;

      svg {
        width: 16px;
        height: 16px;
      }

      &:hover {
        background: $bg-secondary;
        color: $primary-orange;
        border-color: $primary-orange;
      }
    }
  }
}
</style>
