<template>
  <div class="restaurant-card" @click="$emit('click', restaurant)">
    <div class="restaurant-card__image">
      <img :src="coverImageUrl" :alt="restaurant.name" />
      <div class="restaurant-card__badge">
        <!-- Price Range -->
        {{ '$20 - $40' }}
      </div>
    </div>

    <div class="restaurant-card__content">
      <div class="restaurant-card__header">
        <h3 class="restaurant-card__name">{{ restaurant.name }}</h3>
        <!-- Cuisine Type -->
        <span class="restaurant-card__cuisine">{{ 'cuisine-name' }}</span>
      </div>

      <div class="restaurant-card__rating">
        <!-- Review Count -->
        <StarRating :rating="restaurant.rating" size="medium" :precision="1" />
      </div>

      <div class="restaurant-card__info">
        <div class="restaurant-card__distance">
          <svg class="restaurant-card__icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" />
            <circle cx="12" cy="10" r="3" />
          </svg>
          {{ restaurant.distance ?? '5.5km' }}
        </div>
        <div class="restaurant-card__time">
          <svg class="restaurant-card__icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="12" cy="12" r="10" />
            <polyline points="12,6 12,12 16,14" />
          </svg>
          {{ restaurant.estimatedTime ?? '30 mins' }}
        </div>
      </div>

      <div class="restaurant-card__tags">
        <span v-for="tag in restaurant.tags" :key="tag" class="restaurant-card__tag" :class="{
          'restaurant-card__tag--open': tag === 'Open Now',
          'restaurant-card__tag--halal': tag === 'Halal'
        }">
          {{ tag }}
        </span>
      </div>

      <button class="restaurant-card__menu-btn" @click.stop="viewDetails">
        View Details
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Restaurant } from '~/types'

const props = defineProps({
  restaurant: {
    type: Object as PropType<Restaurant>,
    required: true
  }
})

defineEmits(['click'])

// Get cover image from photos array
const coverImageUrl = computed(() => {
  // Check if restaurant has the new photos structure (RestaurantPhotos[])
  const photos = props.restaurant.photos as any

  if (photos && Array.isArray(photos)) {
    // Try to find a photo object with isCover property
    const photoWithCover = photos.find((photo: any) =>
      typeof photo === 'object' && photo !== null && photo.isCover === true
    )

    if (photoWithCover && photoWithCover.downloadUrl) {
      return photoWithCover.downloadUrl
    }
  }

  // Fallback to a default image if no cover photo is found
  return 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=400&h=300&fit=crop'
})

const restaurantsStore = useRestaurantsStore()

const viewDetails = () => {
  navigateTo(`/restaurant/${props.restaurant.id}`)
}
</script>

<style lang="scss" scoped>
.restaurant-card {
  background: $white;
  border-radius: $border-radius-xl;
  overflow: hidden;
  box-shadow: $shadow-light;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid $border-light;

  &:hover {
    transform: translateY(-4px);
    box-shadow: $shadow-heavy;
    border-color: $primary-orange;
  }

  &__image {
    position: relative;
    height: 200px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }

    &:hover img {
      transform: scale(1.05);
    }
  }

  &__badge {
    position: absolute;
    top: $spacing-md;
    right: $spacing-md;
    background: rgba(0, 0, 0, 0.8);
    color: $white;
    padding: $spacing-xs $spacing-sm;
    border-radius: $border-radius-sm;
    font-family: $font-secondary;
    font-size: $font-xs;
    font-weight: $font-semibold;
  }

  &__content {
    padding: $spacing-lg;

    @media (max-width: $mobile) {
      padding: $spacing-md;
    }
  }

  &__header {
    margin-bottom: $spacing-md;
  }

  &__name {
    font-family: $font-primary;
    font-size: $font-xl;
    font-weight: $font-semibold;
    color: $text-primary;
    margin-bottom: $spacing-xs;
    line-height: $leading-tight;
  }

  &__cuisine {
    font-family: $font-secondary;
    font-size: $font-sm;
    color: $text-secondary;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  &__rating {
    margin-bottom: $spacing-md;
  }

  &__info {
    display: flex;
    gap: $spacing-lg;
    margin-bottom: $spacing-md;

    @media (max-width: $mobile) {
      gap: $spacing-md;
    }
  }

  &__distance,
  &__time {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    font-family: $font-secondary;
    font-size: $font-sm;
    color: $text-secondary;
  }

  &__icon {
    width: 14px;
    height: 14px;
    color: $medium-gray;
  }

  &__tags {
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-xs;
    margin-bottom: $spacing-lg;
  }

  &__tag {
    padding: $spacing-xs $spacing-sm;
    border-radius: $border-radius-sm;
    font-family: $font-secondary;
    font-size: $font-xs;
    font-weight: $font-medium;
    background: $light-gray;
    color: $text-secondary;
    border: 1px solid $border-light;

    &--open {
      background: rgba(76, 175, 80, 0.1);
      color: $secondary-green;
      border-color: rgba(76, 175, 80, 0.3);
    }

    &--halal {
      background: rgba(33, 150, 243, 0.1);
      color: $secondary-blue;
      border-color: rgba(33, 150, 243, 0.3);
    }
  }

  &__menu-btn {
    width: 100%;
    background: linear-gradient(135deg, $primary-red 0%, $primary-orange 100%);
    color: $white;
    border: none;
    padding: $spacing-sm $spacing-md;
    border-radius: $border-radius-md;
    font-family: $font-secondary;
    font-size: $font-sm;
    font-weight: $font-semibold;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: $shadow-medium;
    }
  }
}
</style>
