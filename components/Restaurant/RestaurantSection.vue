<template>

    <div class="restaurant-section">
        <h2 class="restaurant-section__title">{{ title }}</h2>
        <div class="restaurant-section__content">
            <slot />
        </div>
    </div>
</template>

<script lang="ts" setup>
defineProps({
    title: {
        type: String,
        default: 'Section Title'
    }
})
</script>

<style lang="scss" scoped>
.restaurant-section {
    margin-bottom: $spacing-xxl;

    &:last-child {
        margin-bottom: 0;
    }

    &__title {
        font-family: $font-primary;
        font-size: $font-2xl;
        font-weight: $font-bold;
        color: $text-primary;
        margin: 0 0 $spacing-lg 0;
        padding-bottom: $spacing-sm;
        border-bottom: 2px solid $primary-orange;
        display: flex;
        align-items: center;
        gap: $spacing-sm;

        @media (max-width: $tablet) {
            font-size: $font-xl;
        }

        &::before {
            content: '';
            width: 4px;
            height: 24px;
            background: $primary-orange;
            border-radius: 2px;
        }
    }
}
</style>