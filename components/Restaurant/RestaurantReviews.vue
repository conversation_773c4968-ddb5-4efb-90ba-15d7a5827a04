<template>
  <div class="restaurant-reviews">
    <!-- Reviews Summary -->
    <div class="restaurant-reviews__summary">
      <div class="restaurant-reviews__rating">
        <div class="restaurant-reviews__rating-score">
          {{ restaurant.rating }}
        </div>
        <div class="restaurant-reviews__rating-details">
          <StarRating 
            :rating="restaurant.rating" 
            size="large"
            :show-rating="false"
          />
          <p class="restaurant-reviews__rating-text">
            Based on {{ totalReviews }} reviews
          </p>
        </div>
      </div>
      
      <div class="restaurant-reviews__breakdown">
        <div
          v-for="rating in [5, 4, 3, 2, 1]"
          :key="rating"
          class="restaurant-reviews__breakdown-item"
        >
          <span class="restaurant-reviews__breakdown-label">{{ rating }} stars</span>
          <div class="restaurant-reviews__breakdown-bar">
            <div
              class="restaurant-reviews__breakdown-fill"
              :style="{ width: `${totalReviews > 0 ? (ratingBreakdown[rating] / totalReviews) * 100 : 0}%` }"
            ></div>
          </div>
          <span class="restaurant-reviews__breakdown-count">{{ ratingBreakdown[rating] || 0 }}</span>
        </div>
      </div>
    </div>

    <!-- Write Review Button -->
    <div class="restaurant-reviews__actions">
      <button class="restaurant-reviews__write-btn" @click="showWriteReview = true">
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
          <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
        </svg>
        Write a Review
      </button>
    </div>

    <!-- Reviews List -->
    <div class="restaurant-reviews__list">
      <div
        v-for="review in displayedReviews"
        :key="review.place_id + review.time"
        class="review-item"
      >
        <div class="review-item__header">
          <div class="review-item__avatar">
            <img src="/avatar.png" :alt="review.author_name" />
          </div>
          <div class="review-item__meta">
            <h4 class="review-item__author">{{ review.author_name }}</h4>
            <div class="review-item__rating">
              <StarRating
                :rating="review.rating"
                size="small"
                :show-rating="false"
                :show-review-count="false"
              />
              <span class="review-item__date">{{ formatDate(review.time) }}</span>
            </div>
          </div>
        </div>
        
        <div class="review-item__content">
          <p class="review-item__text">{{ review.text }}</p>

          <div class="review-item__actions">
            <button class="review-item__helpful">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"/>
              </svg>
              Helpful
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Load More -->
    <div class="restaurant-reviews__load-more" v-if="hasMoreReviews">
      <button class="restaurant-reviews__load-btn" @click="loadMoreReviews">
        Load More Reviews
      </button>
    </div>

    <!-- Write Review Modal -->
    <div v-if="showWriteReview" class="review-modal">
      <div class="review-modal__overlay" @click="showWriteReview = false"></div>
      <div class="review-modal__content">
        <h3>Write a Review</h3>
        <p>Share your experience at {{ restaurant.name }}</p>
        <form @submit.prevent="submitReview">
          <div class="review-form__rating">
            <label>Rating</label>
            <div class="review-form__stars">
              <button 
                v-for="star in 5" 
                :key="star"
                type="button"
                class="review-form__star"
                :class="{ 'review-form__star--active': star <= newReview.rating }"
                @click="newReview.rating = star"
              >
                ★
              </button>
            </div>
          </div>
          
          <div class="review-form__field">
            <label for="review-text">Your Review</label>
            <textarea 
              id="review-text"
              v-model="newReview.text"
              placeholder="Tell others about your experience..."
              rows="4"
              required
            ></textarea>
          </div>
          
          <div class="review-form__actions">
            <button type="button" @click="showWriteReview = false">Cancel</button>
            <button type="submit" class="btn--primary">Submit Review</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

const restaurantStore = useRestaurantsStore()
const { restaurantReviews, hasMoreReviews } = storeToRefs(restaurantStore)

interface Props {
  restaurant: any
}

const props = defineProps<Props>()

const showWriteReview = ref(false)

const newReview = ref({
  rating: 5,
  text: ''
})

// Use reviews from store, sorted by latest first
const displayedReviews = computed(() => {
  const reviews = restaurantReviews.value || []
  // Sort by time (timestamp) in descending order (latest first)
  return [...reviews].sort((a, b) => b.time - a.time)
})

const totalReviews = computed(() => {
  // For now, use the displayed reviews count
  // In a real app, this would come from the restaurant data
  return displayedReviews.value.length
})

const ratingBreakdown = computed(() => {
  const breakdown: Record<number, number> = { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 }
  displayedReviews.value.forEach(review => {
    if (review.rating >= 1 && review.rating <= 5) {
      breakdown[review.rating]++
    }
  })
  return breakdown
})

const loadMoreReviews = async () => {
  await restaurantStore.fetchNextReview()
}

// Generate avatar URL based on author name
// const getReviewerAvatar = (authorName: string) => {
//   // Generate a consistent avatar based on the author name
//   const avatars = [
//     'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
//     'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
//     'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
//     'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
//     'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face'
//   ]

//   // Use a simple hash of the author name to pick an avatar
//   let hash = 0
//   for (let i = 0; i < authorName.length; i++) {
//     hash = ((hash << 5) - hash + authorName.charCodeAt(i)) & 0xffffffff
//   }

//   return avatars[Math.abs(hash) % avatars.length]
// }

const formatDate = (timestamp: number) => {
  try {
    // Convert timestamp to Date object
    // Handle both seconds and milliseconds timestamps
    const date = timestamp > 1000000000000
      ? new Date(timestamp)           // Milliseconds timestamp
      : new Date(timestamp * 1000)    // Seconds timestamp

    if (!isNaN(date.getTime())) {
      return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }).format(date)
    }

    // Fallback: return a default message if parsing fails
    return 'Date unavailable'
  } catch (error) {
    console.warn('Error formatting timestamp:', timestamp, error)
    return 'Date unavailable'
  }
}

const submitReview = () => {
  // In a real app, this would submit to an API
  console.log('Submitting review:', newReview.value)
  showWriteReview.value = false
  newReview.value = { rating: 5, text: '' }
}
</script>

<style lang="scss" scoped>
.restaurant-reviews {
  &__summary {
    display: flex;
    gap: $spacing-xxl;
    margin-bottom: $spacing-xxl;
    padding: $spacing-xl;
    background: $bg-secondary;
    border-radius: $border-radius-lg;

    @media (max-width: $tablet) {
      flex-direction: column;
      gap: $spacing-lg;
    }
  }

  &__rating {
    display: flex;
    align-items: center;
    gap: $spacing-lg;
  }

  &__rating-score {
    font-family: $font-primary;
    font-size: 4rem;
    font-weight: $font-bold;
    color: $primary-orange;
    line-height: 1;
  }

  &__rating-text {
    font-family: $font-secondary;
    font-size: $font-sm;
    color: $text-secondary;
    margin: $spacing-xs 0 0 0;
  }

  &__breakdown {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: $spacing-sm;
  }

  &__breakdown-item {
    display: flex;
    align-items: center;
    gap: $spacing-md;
    font-family: $font-secondary;
    font-size: $font-sm;
  }

  &__breakdown-label {
    width: 60px;
    color: $text-secondary;
  }

  &__breakdown-bar {
    flex: 1;
    height: 8px;
    background: $border-light;
    border-radius: $border-radius-sm;
    overflow: hidden;
  }

  &__breakdown-fill {
    height: 100%;
    background: $primary-orange;
    transition: width 0.3s ease;
  }

  &__breakdown-count {
    width: 30px;
    text-align: right;
    color: $text-secondary;
  }

  &__actions {
    margin-bottom: $spacing-xxl;
  }

  &__write-btn {
    display: flex;
    align-items: center;
    gap: $spacing-sm;
    background: $primary-orange;
    color: $white;
    border: none;
    padding: $spacing-md $spacing-lg;
    border-radius: $border-radius-lg;
    font-family: $font-secondary;
    font-weight: $font-semibold;
    cursor: pointer;
    transition: all 0.3s ease;

    svg {
      width: 16px;
      height: 16px;
    }

    &:hover {
      background: color.adjust($primary-orange, $lightness: -10%);
      transform: translateY(-2px);
    }
  }

  &__list {
    display: flex;
    flex-direction: column;
    gap: $spacing-xl;
  }

  &__load-more {
    text-align: center;
    margin-top: $spacing-xxl;
  }

  &__load-btn {
    background: transparent;
    color: $primary-orange;
    border: 1px solid $primary-orange;
    padding: $spacing-md $spacing-xl;
    border-radius: $border-radius-lg;
    font-family: $font-secondary;
    font-weight: $font-semibold;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: $primary-orange;
      color: $white;
    }
  }
}

.review-item {
  padding: $spacing-xl;
  background: $white;
  border-radius: $border-radius-lg;
  border: 1px solid $border-light;

  &__header {
    display: flex;
    gap: $spacing-md;
    margin-bottom: $spacing-md;
  }

  &__avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  &__meta {
    flex: 1;
  }

  &__author {
    font-family: $font-primary;
    font-size: $font-base;
    font-weight: $font-semibold;
    color: $text-primary;
    margin: 0 0 $spacing-xs 0;
  }

  &__rating {
    display: flex;
    align-items: center;
    gap: $spacing-sm;
  }

  &__date {
    font-family: $font-secondary;
    font-size: $font-xs;
    color: $text-muted;
  }

  &__text {
    font-family: $font-secondary;
    line-height: $leading-relaxed;
    color: $text-primary;
    margin: 0 0 $spacing-md 0;
  }

  &__actions {
    display: flex;
    gap: $spacing-md;
  }

  &__helpful {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    background: transparent;
    border: 1px solid $border-light;
    color: $text-secondary;
    padding: $spacing-xs $spacing-sm;
    border-radius: $border-radius-md;
    font-family: $font-secondary;
    font-size: $font-xs;
    cursor: pointer;
    transition: all 0.3s ease;

    svg {
      width: 12px;
      height: 12px;
    }

    &:hover {
      border-color: $primary-orange;
      color: $primary-orange;
    }
  }
}

.review-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: $z-modal;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-lg;

  &__overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
  }

  &__content {
    position: relative;
    background: $white;
    border-radius: $border-radius-xl;
    padding: $spacing-xxl;
    max-width: 500px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;

    h3 {
      font-family: $font-primary;
      margin: 0 0 $spacing-sm 0;
    }

    p {
      color: $text-secondary;
      margin: 0 0 $spacing-xl 0;
    }
  }
}

.review-form {
  &__rating {
    margin-bottom: $spacing-lg;

    label {
      display: block;
      font-family: $font-secondary;
      font-weight: $font-semibold;
      margin-bottom: $spacing-sm;
    }
  }

  &__stars {
    display: flex;
    gap: $spacing-xs;
  }

  &__star {
    background: none;
    border: none;
    font-size: $font-2xl;
    color: $border-medium;
    cursor: pointer;
    transition: color 0.2s ease;

    &--active,
    &:hover {
      color: #ffd700;
    }
  }

  &__field {
    margin-bottom: $spacing-lg;

    label {
      display: block;
      font-family: $font-secondary;
      font-weight: $font-semibold;
      margin-bottom: $spacing-sm;
    }

    textarea {
      width: 100%;
      padding: $spacing-md;
      border: 1px solid $border-light;
      border-radius: $border-radius-md;
      font-family: $font-secondary;
      resize: vertical;

      &:focus {
        outline: none;
        border-color: $primary-orange;
      }
    }
  }

  &__actions {
    display: flex;
    gap: $spacing-md;
    justify-content: flex-end;

    button {
      padding: $spacing-sm $spacing-lg;
      border-radius: $border-radius-md;
      font-family: $font-secondary;
      font-weight: $font-semibold;
      cursor: pointer;
      transition: all 0.3s ease;

      &:first-child {
        background: transparent;
        border: 1px solid $border-light;
        color: $text-secondary;

        &:hover {
          border-color: $text-secondary;
        }
      }

      &.btn--primary {
        background: $primary-orange;
        color: $white;
        border: none;

        &:hover {
          background: color.adjust($primary-orange, $lightness: -10%);
        }
      }
    }
  }
}
</style>
