<template>
  <section class="owner-cta">
    <div class="container">
      <div class="owner-cta__content">
        <div class="owner-cta__text">
          <h2 class="owner-cta__title">Own a Restaurant?</h2>
          <p class="owner-cta__subtitle">
            Join our directory and connect with thousands of food lovers
          </p>

          <div class="owner-cta__benefits">
            <div class="owner-cta__benefit">
              <div class="owner-cta__benefit-icon">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" />
                  <circle cx="12" cy="10" r="3" />
                </svg>
              </div>
              <div class="owner-cta__benefit-text">
                <h3>Get Discovered</h3>
                <p>Reach new customers in your area</p>
              </div>
            </div>

            <div class="owner-cta__benefit">
              <div class="owner-cta__benefit-icon">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path
                    d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                </svg>
              </div>
              <div class="owner-cta__benefit-text">
                <h3>Manage Reviews</h3>
                <p>Respond to feedback and build reputation</p>
              </div>
            </div>

            <div class="owner-cta__benefit">
              <div class="owner-cta__benefit-icon">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
                  <line x1="16" y1="2" x2="16" y2="6" />
                  <line x1="8" y1="2" x2="8" y2="6" />
                  <line x1="3" y1="10" x2="21" y2="10" />
                </svg>
              </div>
              <div class="owner-cta__benefit-text">
                <h3>Promote Your Menu</h3>
                <p>Showcase your dishes and specialties</p>
              </div>
            </div>
          </div>

          <div class="owner-cta__actions">
            <button class="owner-cta__primary-btn" @click="handleJoinDirectory">
              Join Our Directory
            </button>
            <button class="owner-cta__secondary-btn" @click="handleLearnMore">
              Learn More
            </button>
          </div>
        </div>

        <div class="owner-cta__image">
          <img
            src="https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
            alt="Restaurant owner managing business" />
          <div class="owner-cta__image-overlay">
            <div class="owner-cta__stats">
              <div class="owner-cta__stat">
                <span class="owner-cta__stat-number">500+</span>
                <span class="owner-cta__stat-label">Partner Restaurants</span>
              </div>
              <div class="owner-cta__stat">
                <span class="owner-cta__stat-number">50K+</span>
                <span class="owner-cta__stat-label">Monthly Visitors</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
const handleJoinDirectory = () => {
  console.log('Join directory clicked')
  // Handle navigation to registration page
}

const handleLearnMore = () => {
  console.log('Learn more clicked')
  // Handle navigation to info page
}
</script>

<style lang="scss" scoped>
.owner-cta {
  padding: $spacing-xxl 0;
  background: linear-gradient(135deg,
      rgba(211, 47, 47, 0.05) 0%,
      rgba(255, 107, 53, 0.05) 50%,
      rgba(255, 215, 0, 0.05) 100%);

  @media (max-width: $tablet) {
    padding: $spacing-xl 0;
  }

  @media (max-width: $mobile) {
    padding: $spacing-lg 0;
  }

  &__content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: $spacing-xxl;
    align-items: center;

    @media (max-width: $desktop) {
      grid-template-columns: 1fr;
      gap: $spacing-xl;
      text-align: center;
    }
  }

  &__title {
    font-family: $font-primary;
    font-size: $font-4xl;
    font-weight: $font-bold;
    color: $text-primary;
    margin-bottom: $spacing-md;

    @media (max-width: $tablet) {
      font-size: $font-3xl;
    }
  }

  &__subtitle {
    font-family: $font-secondary;
    font-size: $font-xl;
    color: $text-secondary;
    margin-bottom: $spacing-xxl;
    line-height: $leading-relaxed;

    @media (max-width: $tablet) {
      font-size: $font-lg;
    }
  }

  &__benefits {
    margin-bottom: $spacing-xxl;

    @media (max-width: $mobile) {
      margin-bottom: $spacing-xl;
    }
  }

  &__benefit {
    display: flex;
    align-items: flex-start;
    gap: $spacing-md;
    margin-bottom: $spacing-lg;

    @media (max-width: $desktop) {
      justify-content: flex-start;
      text-align: left;
      max-width: 400px;
      margin: 0 auto $spacing-lg auto;
    }

    @media (max-width: $tablet) {
      gap: $spacing-sm;
      margin-bottom: $spacing-md;
    }

    @media (max-width: $mobile) {
      flex-direction: column;
      align-items: center;
      text-align: center;
      gap: $spacing-sm;
      margin-bottom: $spacing-lg;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__benefit-icon {
    flex-shrink: 0;
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, $primary-red 0%, $primary-orange 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    svg {
      width: 24px;
      height: 24px;
      color: $white;
    }
  }

  &__benefit-text {
    flex: 1;

    @media (max-width: $mobile) {
      text-align: center;
    }

    h3 {
      font-family: $font-primary;
      font-size: $font-lg;
      font-weight: $font-semibold;
      color: $text-primary;
      margin-bottom: $spacing-xs;

      @media (max-width: $tablet) {
        font-size: $font-base;
      }
    }

    p {
      font-family: $font-secondary;
      font-size: $font-base;
      color: $text-secondary;
      line-height: $leading-relaxed;

      @media (max-width: $tablet) {
        font-size: $font-sm;
      }
    }
  }

  &__actions {
    display: flex;
    gap: $spacing-md;

    @media (max-width: $desktop) {
      justify-content: center;
      max-width: 400px;
      margin: 0 auto;
    }

    @media (max-width: $tablet) {
      flex-direction: column;
      gap: $spacing-sm;
    }

    @media (max-width: $mobile) {
      width: 100%;
      max-width: 300px;
    }
  }

  &__primary-btn {
    background: linear-gradient(135deg, $primary-red 0%, $primary-orange 100%);
    color: $white;
    border: none;
    padding: $spacing-md $spacing-xl;
    border-radius: $border-radius-lg;
    font-family: $font-secondary;
    font-size: $font-base;
    font-weight: $font-semibold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: $shadow-medium;
    white-space: nowrap;

    @media (max-width: $tablet) {
      width: 100%;
      padding: $spacing-md;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: $shadow-heavy;

      @media (max-width: $mobile) {
        transform: none;
      }
    }
  }

  &__secondary-btn {
    background: transparent;
    color: $primary-orange;
    border: 2px solid $primary-orange;
    padding: $spacing-md $spacing-xl;
    border-radius: $border-radius-lg;
    font-family: $font-secondary;
    font-size: $font-base;
    font-weight: $font-semibold;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;

    @media (max-width: $tablet) {
      width: 100%;
      padding: $spacing-md;
    }

    &:hover {
      background: $primary-orange;
      color: $white;
      transform: translateY(-2px);

      @media (max-width: $mobile) {
        transform: none;
      }
    }
  }

  &__image {
    position: relative;
    border-radius: $border-radius-xl;
    overflow: hidden;
    box-shadow: $shadow-heavy;

    img {
      width: 100%;
      height: 400px;
      object-fit: cover;
    }
  }

  &__image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: $spacing-xxl $spacing-lg $spacing-lg;
  }

  &__stats {
    display: flex;
    justify-content: space-around;
  }

  &__stat {
    text-align: center;
    color: $white;
  }

  &__stat-number {
    display: block;
    font-family: $font-primary;
    font-size: $font-3xl;
    font-weight: $font-bold;
    margin-bottom: $spacing-xs;
  }

  &__stat-label {
    font-family: $font-secondary;
    font-size: $font-sm;
    opacity: 0.9;
  }
}

.container {
  max-width: $large-desktop;
  margin: 0 auto;
  padding: 0 $spacing-lg;

  @media (max-width: $tablet) {
    padding: 0 $spacing-md;
  }
}
</style>
