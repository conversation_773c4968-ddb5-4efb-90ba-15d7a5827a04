<template>
  <section class="hero">
    <div class="hero__background"></div>

    <div class="hero__content">
      <div class="container">
        <div class="hero__text">
          <h1 class="hero__title">
            Discover Authentic
            <span class="hero__title--highlight">Asian Table</span>
          </h1>
          <p class="hero__subtitle">
            Find the best Asian restaurants, from street food to fine dining, all in one place
          </p>
        </div>

        <SearchBar class="hero__search" />

        <div class="hero__tags">
          <h3 class="hero__tags-title">Popular Searches:</h3>
          <div class="hero__tags-list">
            <button v-for="tag in popularTags" :key="tag" class="hero__tag" @click="handleTagClick(tag)">
              {{ tag }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
const popularTags = ref([
  'Sushi', 'Ramen', 'Dim Sum', '<PERSON><PERSON>', 'Pho', 'Korean BBQ', 'Thai Curry', 'Dumplings'
])

const handleTagClick = (tag) => {
  // Handle tag click - could navigate to search results
  console.log('Searching for:', tag)
}
</script>

<style lang="scss" scoped>
.hero {
  position: relative;
  height: 100vh;
  min-height: 600px;
  display: flex;
  align-items: center;
  overflow: hidden;

  @media (max-width: $tablet) {
    min-height: 500px;
    height: 100vh;
  }

  @media (max-width: $mobile) {
    min-height: 100vh;
    padding: $spacing-md 0;
  }

  // iPhone SE specific optimization
  @media (max-width: 375px) and (max-height: 667px) {
    height: 100vh;
    min-height: 667px;
    padding: $spacing-sm 0;
  }

  &__background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://images.unsplash.com/photo-1504674900247-0877df9cc836?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    z-index: -1;

    // Fallback gradient if image doesn't load
    background-color: $primary-red;

    // Gradient overlay for better text contrast and Asian theme
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg,
          rgba(211, 47, 47, 0.7) 0%,
          rgba(255, 107, 53, 0.7) 50%,
          rgba(255, 215, 0, 0.6) 100%);
      z-index: 1;
    }

    // Additional dark overlay for better text readability
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.2);
      z-index: 2;
    }
  }



  &__content {
    width: 100%;
    z-index: 10;
    position: relative;
  }

  &__text {
    text-align: center;
    margin-bottom: $spacing-xxl;

    @media (max-width: $mobile) {
      margin-bottom: $spacing-xl;
    }

    // iPhone SE optimization
    @media (max-width: 375px) and (max-height: 667px) {
      margin-bottom: $spacing-lg;
    }
  }

  &__title {
    font-family: $font-primary;
    font-size: $font-5xl;
    font-weight: $font-bold;
    color: $text-white;
    line-height: $leading-tight;
    margin-bottom: $spacing-lg;

    &--highlight {
      color: $primary-gold;
      display: block;
    }

    @media (max-width: $tablet) {
      font-size: $font-4xl;
    }

    @media (max-width: $mobile) {
      font-size: $font-3xl;
    }

    // iPhone SE optimization
    @media (max-width: 375px) and (max-height: 667px) {
      font-size: $font-2xl;
      margin-bottom: $spacing-md;
      line-height: $leading-snug;
    }
  }

  &__subtitle {
    font-family: $font-secondary;
    font-size: $font-xl;
    color: rgba(255, 255, 255, 0.9);
    line-height: $leading-relaxed;
    max-width: 600px;
    margin: 0 auto;

    @media (max-width: $tablet) {
      font-size: $font-lg;
    }

    // iPhone SE optimization
    @media (max-width: 375px) and (max-height: 667px) {
      font-size: $font-base;
      max-width: 300px;
      line-height: $leading-normal;
    }
  }

  &__search {
    margin-bottom: $spacing-xxl;

    @media (max-width: $mobile) {
      margin-bottom: $spacing-xl;
    }

    // iPhone SE optimization
    @media (max-width: 375px) and (max-height: 667px) {
      margin-bottom: $spacing-lg;
    }
  }

  &__tags {
    text-align: center;

    // Hide on iPhone SE to save space
    @media (max-width: 375px) and (max-height: 667px) {
      display: none;
    }
  }

  &__tags-title {
    font-family: $font-secondary;
    font-size: $font-base;
    font-weight: $font-medium;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: $spacing-md;
  }

  &__tags-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: $spacing-sm;
    max-width: 800px;
    margin: 0 auto;
  }

  &__tag {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: $text-white;
    padding: $spacing-sm $spacing-md;
    border-radius: $border-radius-lg;
    font-family: $font-secondary;
    font-size: $font-sm;
    font-weight: $font-medium;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      border-color: rgba(255, 255, 255, 0.5);
      transform: translateY(-2px);
    }
  }
}

.container {
  max-width: $large-desktop;
  margin: 0 auto;
  padding: 0 $spacing-lg;

  @media (max-width: $tablet) {
    padding: 0 $spacing-md;
  }

  // iPhone SE optimization
  @media (max-width: 375px) and (max-height: 667px) {
    padding: 0 $spacing-sm;
  }
}
</style>
