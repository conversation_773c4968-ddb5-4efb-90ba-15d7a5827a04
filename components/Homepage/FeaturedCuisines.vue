<template>
  <section class="featured-cuisines">
    <div class="container">
      <div class="featured-cuisines__header">
        <h2 class="featured-cuisines__title">Featured Cuisines</h2>
        <p class="featured-cuisines__subtitle">
          Explore diverse Asian flavors from across the continent
        </p>
      </div>

      <div class="featured-cuisines__carousel">
        <div class="featured-cuisines__track" ref="carouselTrack">
          <div v-for="cuisine in cuisines" :key="cuisine.id" class="featured-cuisines__card"
            @click="handleCuisineClick(cuisine)">
            <div class="featured-cuisines__image">
              <img :src="cuisine.image" :alt="cuisine.name" />
              <div class="featured-cuisines__overlay">
                <svg class="featured-cuisines__arrow" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="m9 18 6-6-6-6" />
                </svg>
              </div>
            </div>
            <div class="featured-cuisines__content">
              <h3 class="featured-cuisines__name">{{ cuisine.name }}</h3>
              <p class="featured-cuisines__description">{{ cuisine.description }}</p>
              <span class="featured-cuisines__count">Explore {{ getRestaurantCount(cuisine.restaurantCount) }}+ {{
                cuisine.name }} Spots</span>
            </div>
          </div>
        </div>

        <div class="featured-cuisines__controls">
          <button class="featured-cuisines__control featured-cuisines__control--prev" @click="scrollCarousel('prev')">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="m15 18-6-6 6-6" />
            </svg>
          </button>
          <button class="featured-cuisines__control featured-cuisines__control--next" @click="scrollCarousel('next')">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="m9 18 6-6-6-6" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
// Import cuisine data from JSON file
import cuisineData from '@/data/cuisines.json'

const carouselTrack = ref(null)
const cuisines = ref(cuisineData)

const handleCuisineClick = (cuisine) => {
  console.log('Selected cuisine:', cuisine.name)
  // Navigate to browse page with cuisine filter
  navigateTo(`/browse?cuisine=${encodeURIComponent(cuisine.name)}`)
}

const scrollCarousel = (direction) => {
  if (!carouselTrack.value) return

  const cardWidth = 320 // Card width + gap
  const scrollAmount = direction === 'next' ? cardWidth : -cardWidth

  carouselTrack.value.scrollBy({
    left: scrollAmount,
    behavior: 'smooth'
  })
}

const getRestaurantCount = (count) => {
  return Math.floor(count / 10) * 10
}
</script>

<style lang="scss" scoped>
.featured-cuisines {
  padding: $spacing-xxl 0;
  background: $bg-secondary;

  @media (max-width: $tablet) {
    padding: $spacing-xl 0;
  }

  @media (max-width: $mobile) {
    padding: $spacing-lg 0;
  }

  &__header {
    text-align: center;
    margin-bottom: $spacing-xxl;

    @media (max-width: $mobile) {
      margin-bottom: $spacing-xl;
    }
  }

  &__title {
    font-family: $font-primary;
    font-size: $font-4xl;
    font-weight: $font-bold;
    color: $text-primary;
    margin-bottom: $spacing-md;

    @media (max-width: $tablet) {
      font-size: $font-3xl;
    }
  }

  &__subtitle {
    font-family: $font-secondary;
    font-size: $font-lg;
    color: $text-secondary;
    max-width: 600px;
    margin: 0 auto;
  }

  &__carousel {
    position: relative;
    overflow: hidden;
  }

  &__track {
    display: flex;
    gap: $spacing-lg;
    overflow-x: auto;
    scroll-behavior: smooth;
    padding: $spacing-md 0;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }

    @media (max-width: $tablet) {
      gap: $spacing-md;
      padding: $spacing-sm 0;
    }

    @media (max-width: $mobile) {
      gap: $spacing-sm;
      padding: $spacing-sm $spacing-sm;
    }
  }

  &__card {
    flex: 0 0 300px;
    background: $white;
    border-radius: $border-radius-xl;
    overflow: hidden;
    box-shadow: $shadow-light;
    cursor: pointer;
    transition: all 0.3s ease;

    @media (max-width: $tablet) {
      flex: 0 0 280px;
    }

    @media (max-width: $mobile) {
      flex: 0 0 260px;
      border-radius: $border-radius-lg;
    }

    &:hover {
      transform: translateY(-8px);
      box-shadow: $shadow-heavy;

      .featured-cuisines__overlay {
        opacity: 1;
      }

      @media (max-width: $mobile) {
        transform: translateY(-4px);
      }
    }
  }

  &__image {
    position: relative;
    height: 200px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }

    &:hover img {
      transform: scale(1.1);
    }
  }

  &__overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &__arrow {
    width: 32px;
    height: 32px;
    color: $white;
  }

  &__content {
    padding: $spacing-lg;
  }

  &__name {
    font-family: $font-primary;
    font-size: $font-xl;
    font-weight: $font-semibold;
    color: $text-primary;
    margin-bottom: $spacing-sm;
  }

  &__description {
    font-family: $font-secondary;
    font-size: $font-base;
    color: $text-secondary;
    margin-bottom: $spacing-md;
  }

  &__count {
    font-family: $font-secondary;
    font-size: $font-sm;
    font-weight: $font-medium;
    color: $primary-orange;
  }

  &__controls {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    pointer-events: none;
    padding: 0 $spacing-md;

    @media (max-width: $tablet) {
      display: none;
    }
  }

  &__control {
    width: 48px;
    height: 48px;
    background: $white;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: $shadow-medium;
    cursor: pointer;
    pointer-events: all;
    transition: all 0.3s ease;

    svg {
      width: 20px;
      height: 20px;
      color: $text-primary;
    }

    &:hover {
      background: $primary-orange;
      transform: scale(1.1);

      svg {
        color: $white;
      }
    }
  }
}

.container {
  max-width: $large-desktop;
  margin: 0 auto;
  padding: 0 $spacing-lg;

  @media (max-width: $tablet) {
    padding: 0 $spacing-md;
  }
}
</style>
