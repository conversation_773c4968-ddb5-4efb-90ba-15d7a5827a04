<template>
  <section class="popular-restaurants">
    <div class="container">
      <div class="popular-restaurants__header">
        <div class="popular-restaurants__title-section">
          <h2 class="popular-restaurants__title">Popular Restaurants Near You</h2>
          <LocationStatus :show-change-button="true" />
        </div>
        <p class="popular-restaurants__subtitle">
          Discover highly-rated Asian restaurants in your area
        </p>
      </div>

      <div class="popular-restaurants__grid">
        <RestaurantCard v-for="restaurant in topSixRestaurants" :key="restaurant.id" :restaurant="restaurant"
          @click="handleRestaurantClick(restaurant)" />
      </div>

      <div class="popular-restaurants__footer" v-if="hasMoreRestaurants">
        <button class="popular-restaurants__load-more" @click="handleLoadMore">
          Load More Restaurants
        </button>
      </div>
    </div>
  </section>
</template>

<script setup>

const restaurantsStore = useRestaurantsStore()
const { restaurants } = storeToRefs(restaurantsStore)

// Show only the top 6 restaurants
const topSixRestaurants = computed(() => {
  return restaurants.value.slice(0, 6)
})

// Check if there are more than 6 restaurants
const hasMoreRestaurants = computed(() => {
  return restaurants.value.length > 6
})

const handleRestaurantClick = (restaurant) => {
  console.log('Selected restaurant:', restaurant.name)
  // Handle navigation to restaurant page
}

const handleLoadMore = () => {
  navigateTo('/browse')
}
</script>

<style lang="scss" scoped>
.popular-restaurants {
  padding: $spacing-xxl 0;
  background: $bg-primary;

  @media (max-width: $tablet) {
    padding: $spacing-xl 0;
  }

  @media (max-width: $mobile) {
    padding: $spacing-lg 0;
  }

  &__header {
    text-align: center;
    margin-bottom: $spacing-xxl;
  }

  &__title-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: $spacing-md;
    margin-bottom: $spacing-md;

    @media (max-width: $mobile) {
      gap: $spacing-sm;
    }
  }

  &__title {
    font-family: $font-primary;
    font-size: $font-4xl;
    font-weight: $font-bold;
    color: $text-primary;
    margin: 0;

    @media (max-width: $tablet) {
      font-size: $font-3xl;
    }
  }

  &__subtitle {
    font-family: $font-secondary;
    font-size: $font-lg;
    color: $text-secondary;
    max-width: 600px;
    margin: 0 auto;
  }

  &__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: $spacing-xl;
    margin-bottom: $spacing-xxl;

    @media (max-width: $tablet) {
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: $spacing-lg;
    }

    @media (max-width: $mobile) {
      grid-template-columns: 1fr;
      gap: $spacing-lg;
      margin-bottom: $spacing-xl;
    }
  }

  &__footer {
    text-align: center;
  }

  &__load-more {
    background: transparent;
    border: 2px solid $primary-orange;
    color: $primary-orange;
    padding: $spacing-md $spacing-xxl;
    border-radius: $border-radius-lg;
    font-family: $font-secondary;
    font-size: $font-base;
    font-weight: $font-semibold;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: $primary-orange;
      color: $white;
      transform: translateY(-2px);
    }
  }
}

.container {
  max-width: $large-desktop;
  margin: 0 auto;
  padding: 0 $spacing-lg;

  @media (max-width: $tablet) {
    padding: 0 $spacing-md;
  }
}
</style>
