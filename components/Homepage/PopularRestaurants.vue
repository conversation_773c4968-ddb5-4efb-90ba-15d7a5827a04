<template>
  <section class="popular-restaurants">
    <div class="container">
      <div class="popular-restaurants__header">
        <div class="popular-restaurants__title-section">
          <h2 class="popular-restaurants__title">Popular Restaurants Near You</h2>
          <LocationStatus :show-change-button="true" />
        </div>
        <p class="popular-restaurants__subtitle">
          Discover highly-rated Asian restaurants in your area
        </p>
      </div>

      <div class="popular-restaurants__grid">
        <RestaurantCard v-for="restaurant in restaurants" :key="restaurant.id" :restaurant="restaurant"
          @click="handleRestaurantClick(restaurant)" />
      </div>

      <div class="popular-restaurants__footer">
        <button v-if="canLoadMore" class="popular-restaurants__load-more" @click="handleLoadMore" :disabled="isLoading">
          {{ isLoading ? 'Loading...' : `Load More Restaurants (${loadMoreClickCount}/${maxLoadMoreClicks})` }}
        </button>
        <button v-if="shouldShowViewAll && restaurants.length > 0" class="popular-restaurants__view-all"
          @click="viewAllRestaurants">
          {{ loadMoreClickCount >= maxLoadMoreClicks ? 'View All Restaurants' : 'View All on Map' }}
        </button>
      </div>
    </div>
  </section>
</template>

<script setup>

const restaurantsStore = useRestaurantsStore()
const { restaurants, hasMoreRestaurants } = storeToRefs(restaurantsStore)

const isLoading = ref(false)
const loadMoreClickCount = ref(0)
const maxLoadMoreClicks = 2

// Check if we can load more restaurants (limited to 2 clicks)
const canLoadMore = computed(() => {
  return hasMoreRestaurants.value && !isLoading.value && loadMoreClickCount.value < maxLoadMoreClicks
})

// Check if we should show "View All" button instead of "Load More"
const shouldShowViewAll = computed(() => {
  return loadMoreClickCount.value >= maxLoadMoreClicks || !hasMoreRestaurants.value
})

const handleRestaurantClick = (restaurant) => {
  console.log('Selected restaurant:', restaurant.name)
  // Navigate to restaurant page
  navigateTo(`/restaurant/${restaurant.id}`)
}

const handleLoadMore = async () => {
  if (isLoading.value || !hasMoreRestaurants.value || loadMoreClickCount.value >= maxLoadMoreClicks) return

  isLoading.value = true
  try {
    await restaurantsStore.loadMoreRestaurants(9)
    loadMoreClickCount.value++

    console.log(`Load more clicked: ${loadMoreClickCount.value}/${maxLoadMoreClicks}`)

    // If we've reached the maximum clicks, navigate to browse page after a short delay
    if (loadMoreClickCount.value >= maxLoadMoreClicks) {
      setTimeout(() => {
        console.log('Maximum load more clicks reached, navigating to browse page')
        navigateTo('/browse')
      }, 1000) // 1 second delay to show the loaded content
    }
  } catch (error) {
    console.error('Error loading more restaurants:', error)
  } finally {
    isLoading.value = false
  }
}

const viewAllRestaurants = () => {
  navigateTo('/browse')
}

// Fetch nearby restaurants on component mount
onMounted(async () => {
  if (restaurants.value.length === 0) {
    isLoading.value = true
    try {
      await restaurantsStore.fetchNearbyRestaurants(9)
      console.log('Initial fetch completed:', {
        restaurantCount: restaurants.value.length,
        hasMore: hasMoreRestaurants.value,
        lastDoc: restaurantsStore.lastRestaurantDoc
      })
    } catch (error) {
      console.error('Error fetching nearby restaurants:', error)
    } finally {
      isLoading.value = false
    }
  }
})

// Debug watcher to track state changes
watch([restaurants, hasMoreRestaurants, loadMoreClickCount], ([newRestaurants, newHasMore, newClickCount]) => {
  console.log('PopularRestaurants state changed:', {
    restaurantCount: newRestaurants.length,
    hasMore: newHasMore,
    clickCount: newClickCount,
    canLoadMore: canLoadMore.value,
    shouldShowViewAll: shouldShowViewAll.value
  })
})
</script>

<style lang="scss" scoped>
.popular-restaurants {
  padding: $spacing-xxl 0;
  background: $bg-primary;

  @media (max-width: $tablet) {
    padding: $spacing-xl 0;
  }

  @media (max-width: $mobile) {
    padding: $spacing-lg 0;
  }

  &__header {
    text-align: center;
    margin-bottom: $spacing-xxl;
  }

  &__title-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: $spacing-md;
    margin-bottom: $spacing-md;

    @media (max-width: $mobile) {
      gap: $spacing-sm;
    }
  }

  &__title {
    font-family: $font-primary;
    font-size: $font-4xl;
    font-weight: $font-bold;
    color: $text-primary;
    margin: 0;

    @media (max-width: $tablet) {
      font-size: $font-3xl;
    }
  }

  &__subtitle {
    font-family: $font-secondary;
    font-size: $font-lg;
    color: $text-secondary;
    max-width: 600px;
    margin: 0 auto;
  }

  &__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: $spacing-xl;
    margin-bottom: $spacing-xxl;

    @media (max-width: $tablet) {
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: $spacing-lg;
    }

    @media (max-width: $mobile) {
      grid-template-columns: 1fr;
      gap: $spacing-lg;
      margin-bottom: $spacing-xl;
    }
  }

  &__footer {
    text-align: center;
    display: flex;
    gap: $spacing-md;
    justify-content: center;
    flex-wrap: wrap;
  }

  &__load-more {
    background: $primary-orange;
    color: $white;
    border: none;
    padding: $spacing-md $spacing-xl;
    border-radius: $border-radius-lg;
    font-family: $font-secondary;
    font-size: $font-base;
    font-weight: $font-semibold;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover:not(:disabled) {
      background: color.adjust($primary-orange, $lightness: -10%);
      transform: translateY(-2px);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }

    @media (max-width: $mobile) {
      padding: $spacing-sm $spacing-lg;
      font-size: $font-sm;
    }
  }

  &__view-all {
    background: transparent;
    color: $primary-orange;
    border: 2px solid $primary-orange;
    padding: $spacing-md $spacing-xl;
    border-radius: $border-radius-lg;
    font-family: $font-secondary;
    font-weight: $font-semibold;
    font-size: $font-base;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: $primary-orange;
      color: $white;
      transform: translateY(-2px);
    }

    @media (max-width: $mobile) {
      padding: $spacing-sm $spacing-lg;
      font-size: $font-sm;
    }
  }
}

.container {
  max-width: $large-desktop;
  margin: 0 auto;
  padding: 0 $spacing-lg;

  @media (max-width: $tablet) {
    padding: 0 $spacing-md;
  }
}
</style>
