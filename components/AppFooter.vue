<template>
  <footer class="footer">
    <div class="container">
      <div class="footer__content">
        <div class="footer__section footer__section--brand">
          <div class="footer__logo">
            <h3 class="footer__brand-name">Asian Tables</h3>
            <p class="footer__brand-tagline">Discover Authentic Asian Flavors</p>
          </div>
          <p class="footer__description">
            Your ultimate guide to the best Asian restaurants, from street food to fine dining.
            Connecting food lovers with authentic Asian culinary experiences.
          </p>
          <!-- <div class="footer__social">
            <a href="#" class="footer__social-link" aria-label="Facebook">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path
                  d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
              </svg>
            </a>
            <a href="#" class="footer__social-link" aria-label="Instagram">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path
                  d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.418-3.323c.875-.875 2.026-1.297 3.323-1.297s2.448.422 3.323 1.297c.928.875 1.418 2.026 1.418 3.323s-.49 2.448-1.418 3.244c-.875.807-2.026 1.297-3.323 1.297zm7.83-9.781c-.49 0-.928-.422-.928-.928 0-.49.438-.928.928-.928.49 0 .928.438.928.928 0 .506-.438.928-.928.928zm-4.262 9.781c-2.448 0-4.474-2.026-4.474-4.474s2.026-4.474 4.474-4.474 4.474 2.026 4.474 4.474-2.026 4.474-4.474 4.474z" />
              </svg>
            </a>
            <a href="#" class="footer__social-link" aria-label="Twitter">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path
                  d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z" />
              </svg>
            </a>
          </div> -->
        </div>

        <div class="footer__section">
          <h4 class="footer__section-title">Explore</h4>
          <ul class="footer__links">
            <li v-for="link in exploreLinks" :key="link.name">
              <a :href="link.url" class="footer__link">{{ link.name }}</a>
            </li>
          </ul>
        </div>

        <div class="footer__section">
          <h4 class="footer__section-title">For Restaurants</h4>
          <ul class="footer__links">
            <li v-for="link in restaurantLinks" :key="link.name">
              <a :href="link.url" class="footer__link">{{ link.name }}</a>
            </li>
          </ul>
        </div>

        <div class="footer__section">
          <h4 class="footer__section-title">Company</h4>
          <ul class="footer__links">
            <li v-for="link in companyLinks" :key="link.name">
              <a :href="link.url" class="footer__link">{{ link.name }}</a>
            </li>
          </ul>
        </div>

        <div class="footer__section footer__section--newsletter">
          <!-- <h4 class="footer__section-title">Stay Updated</h4>
          <p class="footer__newsletter-text">
            Get the latest restaurant news and exclusive offers
          </p>
          <form class="footer__newsletter" @submit.prevent="handleNewsletterSubmit">
            <input v-model="email" type="email" class="footer__newsletter-input" placeholder="Enter your email"
              required />
            <button type="submit" class="footer__newsletter-btn">
              Subscribe
            </button>
          </form> -->

          <div class="footer__language">
            <label for="language-select" class="footer__language-label">Language:</label>
            <select id="language-select" v-model="selectedLanguage" class="footer__language-select"
              @change="handleLanguageChange">
              <option value="en">English</option>
              <option value="zh">中文</option>
              <option value="ja">日本語</option>
              <option value="ko">한국어</option>
              <option value="th">ไทย</option>
              <option value="vi">Tiếng Việt</option>
            </select>
          </div>
        </div>
      </div>

      <div class="footer__bottom">
        <div class="footer__legal">
          <p class="footer__copyright">
            © 2024 Asian Tables. All rights reserved.
          </p>
          <div class="footer__legal-links">
            <!-- <a href="#" class="footer__legal-link">Privacy Policy</a> -->
            <a href="https://dailybreadmedia.com.au/terms" class="footer__legal-link">Terms and Privacy</a>
            <!-- <a href="#" class="footer__legal-link">Cookie Policy</a> -->
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
const email = ref('')
const selectedLanguage = ref('en')

const handleNewsletterSubmit = () => {
  console.log('Newsletter signup:', email.value)
  // Handle newsletter subscription
  email.value = ''
}

const handleLanguageChange = () => {
  console.log('Language changed to:', selectedLanguage.value)
  // Handle language change
}

const exploreLinks = [
  { name: 'Find Restaurants', url: '/browse' },
  // { name: 'Browse Cuisines', url: '#' },
  // { name: 'Top Rated', url: '#' },
  // { name: 'New Openings', url: '#' },
  // { name: 'Food Delivery', url: '#' }
]

const restaurantLinks = [
  { name: 'Join Directory', url: '/owner' },
  // { name: 'Business Dashboard', url: '#' },
  // { name: 'Promote Your Menu', url: '#' },
  // { name: 'Analytics', url: '#' },
  // { name: 'Support', url: '#' }
]

const companyLinks = [
  { name: 'About Us', url: '#' },
  // { name: 'Contact', url: '#' },
  // { name: 'Careers', url: '#' },
  // { name: 'Press', url: '#' },
  // { name: 'Blog', url: '#' }
]
</script>

<style lang="scss" scoped>
.footer {
  background: $bg-dark;
  color: $text-white;
  padding: $spacing-xxl 0 $spacing-lg;

  @media (max-width: $tablet) {
    padding: $spacing-xl 0 $spacing-md;
  }

  @media (max-width: $mobile) {
    padding: $spacing-lg 0 $spacing-md;
  }

  &__content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1.5fr;
    gap: $spacing-xxl;
    margin-bottom: $spacing-xxl;

    @media (max-width: $desktop) {
      grid-template-columns: 1fr 1fr 1fr;
      gap: $spacing-xl;
    }

    @media (max-width: $tablet) {
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: $spacing-lg;
      margin-bottom: $spacing-xl;
    }

    @media (max-width: $mobile) {
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: $spacing-md;
      margin-bottom: $spacing-lg;
    }
  }

  &__section {
    &--brand {
      @media (max-width: $desktop) {
        grid-column: 1 / -1;
      }

      @media (max-width: $tablet) {
        grid-column: 1 / -1;
        margin-bottom: $spacing-lg;
      }
    }

    &--newsletter {
      @media (max-width: $desktop) {
        grid-column: 1 / -1;
      }

      @media (max-width: $tablet) {
        grid-column: 1 / -1;
        margin-top: $spacing-lg;
      }
    }
  }

  &__logo {
    margin-bottom: $spacing-lg;
  }

  &__brand-name {
    font-family: $font-primary;
    font-size: $font-2xl;
    font-weight: $font-bold;
    color: $primary-gold;
    margin-bottom: $spacing-xs;
  }

  &__brand-tagline {
    font-family: $font-secondary;
    font-size: $font-sm;
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
  }

  &__description {
    font-family: $font-secondary;
    font-size: $font-base;
    color: rgba(255, 255, 255, 0.8);
    line-height: $leading-relaxed;
    margin-bottom: $spacing-lg;
  }

  &__social {
    display: flex;
    gap: $spacing-md;
  }

  &__social-link {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.7);
    transition: all 0.3s ease;

    svg {
      width: 20px;
      height: 20px;
    }

    &:hover {
      background: $primary-orange;
      color: $white;
      transform: translateY(-2px);
    }
  }

  &__section-title {
    font-family: $font-primary;
    font-size: $font-lg;
    font-weight: $font-semibold;
    color: $white;
    margin-bottom: $spacing-lg;
  }

  &__links {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  &__link {
    display: block;
    font-family: $font-secondary;
    font-size: $font-base;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    padding: $spacing-xs 0;
    transition: color 0.3s ease;

    &:hover {
      color: $primary-orange;
    }
  }

  &__newsletter-text {
    font-family: $font-secondary;
    font-size: $font-base;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: $spacing-lg;
  }

  &__newsletter {
    display: flex;
    gap: $spacing-sm;
    margin-bottom: $spacing-lg;

    @media (max-width: $tablet) {
      gap: $spacing-xs;
    }

    @media (max-width: $mobile) {
      flex-direction: column;
      gap: $spacing-sm;
    }
  }

  &__newsletter-input {
    flex: 1;
    padding: $spacing-sm $spacing-md;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: $border-radius-md;
    background: rgba(255, 255, 255, 0.1);
    color: $white;
    font-family: $font-secondary;
    font-size: $font-base;

    &::placeholder {
      color: rgba(255, 255, 255, 0.5);
    }

    &:focus {
      outline: none;
      border-color: $primary-orange;
    }
  }

  &__newsletter-btn {
    background: $primary-orange;
    color: $white;
    border: none;
    padding: $spacing-sm $spacing-md;
    border-radius: $border-radius-md;
    font-family: $font-secondary;
    font-size: $font-base;
    font-weight: $font-semibold;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;

    &:hover {
      background: color.adjust($primary-orange, $lightness: -10%);
    }
  }

  &__language {
    display: flex;
    align-items: center;
    gap: $spacing-sm;

    @media (max-width: $mobile) {
      flex-direction: column;
      align-items: flex-start;
      gap: $spacing-xs;
    }
  }

  &__language-label {
    font-family: $font-secondary;
    font-size: $font-sm;
    color: rgba(255, 255, 255, 0.7);
  }

  &__language-select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: $border-radius-sm;
    color: $white;
    padding: $spacing-xs $spacing-sm;
    font-family: $font-secondary;
    font-size: $font-base;
    cursor: pointer;
    box-sizing: border-box;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right $spacing-xs center;
    background-size: 12px;
    padding-right: $spacing-lg;
    min-width: 120px;

    &:focus {
      outline: none;
      border-color: $primary-orange;
    }

    @media (max-width: $tablet) {
      font-size: $font-base;
      padding: $spacing-sm $spacing-lg $spacing-sm $spacing-xs;
      min-width: 140px;
    }

    @media (max-width: $mobile) {
      width: 100%;
      margin-top: $spacing-xs;
    }

    option {
      background: $bg-dark;
      color: $white;
      font-size: $font-base;
      padding: $spacing-sm;

      @media (max-width: $tablet) {
        font-size: $font-base;
        padding: $spacing-md;
      }
    }
  }

  &__bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: $spacing-lg;
  }

  &__legal {
    display: flex;
    justify-content: space-between;
    align-items: center;

    @media (max-width: $tablet) {
      flex-direction: column;
      gap: $spacing-md;
      text-align: center;
    }
  }

  &__copyright {
    font-family: $font-secondary;
    font-size: $font-sm;
    color: rgba(255, 255, 255, 0.6);
    margin: 0;
  }

  &__legal-links {
    display: flex;
    gap: $spacing-lg;

    @media (max-width: $mobile) {
      flex-direction: column;
      gap: $spacing-sm;
    }
  }

  &__legal-link {
    font-family: $font-secondary;
    font-size: $font-sm;
    color: rgba(255, 255, 255, 0.6);
    text-decoration: none;
    transition: color 0.3s ease;

    &:hover {
      color: $primary-orange;
    }
  }
}

.container {
  max-width: $large-desktop;
  margin: 0 auto;
  padding: 0 $spacing-lg;

  @media (max-width: $tablet) {
    padding: 0 $spacing-md;
  }
}
</style>
