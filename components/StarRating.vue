<template>
  <div class="star-rating">
    <div class="star-rating__stars">
      <div v-for="star in maxStars" :key="star" class="star-rating__star-container">
        <svg class="star-rating__star star-rating__star--background" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
        </svg>

        <svg class="star-rating__star star-rating__star--filled" viewBox="0 0 24 24" fill="currentColor"
          :style="{ clipPath: getClipPath(star) }">
          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
        </svg>
      </div>
    </div>

    <span v-if="showRating" class="star-rating__text">
      {{ displayRating }}
      <span v-if="showReviewCount && reviewCount" class="star-rating__reviews">
        ({{ reviewCount }} {{ reviewCount === 1 ? 'review' : 'reviews' }})
      </span>
    </span>
  </div>
</template>

<script setup lang="ts">
interface Props {
  rating: number
  maxStars?: number
  size?: 'small' | 'medium' | 'large'
  showRating?: boolean
  showReviewCount?: boolean
  reviewCount?: number
  precision?: number
}

const props = withDefaults(defineProps<Props>(), {
  maxStars: 5,
  size: 'medium',
  showRating: true,
  showReviewCount: true,
  precision: 1
})

// Calculate the fill percentage for each star
const getStarFillPercentage = (starNumber: number): number => {
  const rating = Math.max(0, Math.min(props.maxStars, props.rating))

  if (starNumber <= Math.floor(rating)) {
    // Full star
    return 100
  } else if (starNumber === Math.floor(rating) + 1) {
    // Partial star
    const decimal = rating - Math.floor(rating)
    return decimal * 100
  } else {
    // Empty star
    return 0
  }
}

// Generate clip path for partial star filling
const getClipPath = (starNumber: number): string => {
  const fillPercentage = getStarFillPercentage(starNumber)
  return `inset(0 ${100 - fillPercentage}% 0 0)`
}

// Format rating display
const displayRating = computed(() => {
  return props.rating.toFixed(props.precision)
})
</script>

<style lang="scss" scoped>
.star-rating {
  display: flex;
  align-items: center;
  gap: $spacing-sm;

  &__stars {
    display: flex;
    gap: 2px;
  }

  &__star-container {
    position: relative;
    display: inline-block;
  }

  &__star {
    width: 16px;
    height: 16px;
    transition: all 0.2s ease;

    &--background {
      color: $border-medium;
    }

    &--filled {
      position: absolute;
      top: 0;
      left: 0;
      color: #ffd700; // Gold color for filled stars
    }
  }

  &__text {
    font-family: $font-secondary;
    font-size: $font-sm;
    color: $text-secondary;
    font-weight: $font-medium;
  }

  &__reviews {
    color: $text-muted;
    font-weight: $font-regular;
  }

  // Size variations
  &--small {
    .star-rating__star {
      width: 12px;
      height: 12px;
    }

    .star-rating__text {
      font-size: $font-xs;
    }
  }

  &--large {
    .star-rating__star {
      width: 20px;
      height: 20px;
    }

    .star-rating__text {
      font-size: $font-base;
    }
  }
}

// Add size classes to the component
.star-rating {
  &.star-rating--small {
    .star-rating__star {
      width: 12px;
      height: 12px;
    }

    .star-rating__text {
      font-size: $font-xs;
    }
  }

  &.star-rating--large {
    .star-rating__star {
      width: 20px;
      height: 20px;
    }

    .star-rating__text {
      font-size: $font-base;
    }
  }
}
</style>
