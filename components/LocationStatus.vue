<template>
  <div class="location-status" :class="`location-status--${locationStatus}`">
    <div class="location-status__icon">
      <svg v-if="locationStatus === 'loading'" class="location-status__spinner" viewBox="0 0 24 24">
        <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
          stroke-dasharray="31.416" stroke-dashoffset="31.416">
          <animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416"
            repeatCount="indefinite" />
          <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite" />
        </circle>
      </svg>
      <svg v-else-if="locationStatus === 'user-location'" viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" />
        <circle cx="12" cy="10" r="3" />
      </svg>
      <svg v-else-if="locationStatus === 'error'" viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <circle cx="12" cy="12" r="10" />
        <line x1="15" y1="9" x2="9" y2="15" />
        <line x1="9" y1="9" x2="15" y2="15" />
      </svg>
      <svg v-else viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" />
        <circle cx="12" cy="10" r="3" />
      </svg>
    </div>

    <div class="location-status__content">
      <span class="location-status__text">{{ locationDisplayName }}</span>
      <button v-if="showChangeButton" class="location-status__change-btn" @click="handleChangeLocation">
        {{ isUsingUserLocation ? 'Use Default Location' : 'Enable Location' }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  showChangeButton?: boolean
  compact?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showChangeButton: false,
  compact: false
})

const locationStore = useLocationStore()
const restaurantsStore = useRestaurantsStore()

const {
  locationStatus,
  locationDisplayName,
  isLocationEnabled
} = storeToRefs(locationStore)

const { isUsingUserLocation } = storeToRefs(restaurantsStore)

const handleChangeLocation = async () => {
  if (isUsingUserLocation.value) {
    // Clear user location and use default
    locationStore.clearUserLocation()
    restaurantsStore.recalculateDistances()
  } else {
    // Request user location
    const success = await locationStore.requestUserLocation()
    if (success) {
      restaurantsStore.recalculateDistances()
    }
  }
}
</script>

<style lang="scss" scoped>
.location-status {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  padding: $spacing-sm $spacing-md;
  border-radius: $border-radius-lg;
  font-family: $font-secondary;
  font-size: $font-sm;
  transition: all 0.3s ease;

  &--loading {
    background: rgba(255, 193, 7, 0.1);
    color: #ff9800;
    border: 1px solid rgba(255, 193, 7, 0.3);
  }

  &--user-location {
    background: rgba(76, 175, 80, 0.1);
    color: $secondary-green;
    border: 1px solid rgba(76, 175, 80, 0.3);
  }

  &--error {
    background: rgba(244, 67, 54, 0.1);
    color: #f44336;
    border: 1px solid rgba(244, 67, 54, 0.3);
  }

  &--fallback-location {
    background: rgba(158, 158, 158, 0.1);
    color: $text-secondary;
    border: 1px solid rgba(158, 158, 158, 0.3);
  }

  &__icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;

    svg {
      width: 100%;
      height: 100%;
    }
  }

  &__spinner {
    animation: spin 1s linear infinite;
  }

  &__content {
    display: flex;
    align-items: center;
    gap: $spacing-sm;
    flex: 1;
  }

  &__text {
    font-weight: $font-medium;
  }

  &__change-btn {
    background: none;
    border: none;
    color: currentColor;
    font-family: $font-secondary;
    font-size: $font-xs;
    text-decoration: underline;
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.3s ease;

    &:hover {
      opacity: 1;
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
</style>
