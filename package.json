{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@pinia/nuxt": "^0.11.0", "firebase": "^11.8.1", "firebase-admin": "^13.4.0", "nuxt": "^3.17.4", "pinia": "^3.0.2", "sass": "^1.89.0", "vue": "^3.5.14", "vue-router": "^4.5.1"}, "devDependencies": {"nuxt-lodash": "^2.5.3"}}