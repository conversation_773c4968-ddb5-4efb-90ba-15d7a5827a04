export const useStoreUtils = () =>
{
  /**
 * Converts a Firebase snapshot to an array of objects with keys.
 * 
 * @param firebaseSnapshot The Firebase DataSnapshot to convert.
 * @returns An array of objects with a `key` field and other properties from the snapshot.
 */
  const mapFirebaseKeyToObject = (firebaseSnapshot: Record<string, any> | null) =>
  {
    if (!firebaseSnapshot) return {};
    return Object.keys(firebaseSnapshot).map(key => ({
      key,
      ...firebaseSnapshot[key]
    }));
  };

  // returns useKeyBy to existing lookup table
  const liquifyToLookupTable = (
    anyArray: any[],
    lookupKey: string
  ) =>
  {
    return useKeyBy(anyArray, lookupKey);
  };

  /* Check if lookupTable has all lookupKeys */
  const allLookupKeysAvailable = (
    lookupTable: Record<string, any>,
    lookupKeys: string[]
  ) =>
  {
    let allSlotsAvailable = false;
    lookupKeys.forEach((lookupKey) =>
    {
      if (!lookupTable[lookupKey])
      {
        allSlotsAvailable = false;
        return;
      }
      allSlotsAvailable = true;
    });
    return allSlotsAvailable;
  };

  return {
    mapFirebaseKeyToObject,
    liquifyToLookupTable,
    allLookupKeysAvailable,
  };
};
