import { ref, computed } from 'vue'

export interface GeolocationCoordinates {
  lat: number
  lng: number
}

export interface GeolocationError {
  code: number
  message: string
}

export const useGeolocation = () => {
  const coordinates = ref<GeolocationCoordinates | null>(null)
  const error = ref<GeolocationError | null>(null)
  const loading = ref(false)
  const permissionStatus = ref<'prompt' | 'granted' | 'denied' | 'unknown'>('unknown')

  // Check if geolocation is supported
  const isSupported = computed(() => {
    return typeof navigator !== 'undefined' && 'geolocation' in navigator
  })

  // Get current position
  const getCurrentPosition = async (options?: PositionOptions): Promise<GeolocationCoordinates | null> => {
    if (!isSupported.value) {
      error.value = {
        code: 0,
        message: 'Geolocation is not supported by this browser'
      }
      return null
    }

    loading.value = true
    error.value = null

    try {
      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(
          resolve,
          reject,
          {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 300000, // 5 minutes
            ...options
          }
        )
      })

      const coords: GeolocationCoordinates = {
        lat: position.coords.latitude,
        lng: position.coords.longitude
      }

      coordinates.value = coords
      permissionStatus.value = 'granted'
      loading.value = false

      return coords
    } catch (err: any) {
      loading.value = false
      
      // Handle different types of geolocation errors
      let errorMessage = 'Failed to get location'
      let errorCode = err.code || 0

      switch (err.code) {
        case 1: // PERMISSION_DENIED
          errorMessage = 'Location access denied by user'
          permissionStatus.value = 'denied'
          break
        case 2: // POSITION_UNAVAILABLE
          errorMessage = 'Location information unavailable'
          break
        case 3: // TIMEOUT
          errorMessage = 'Location request timed out'
          break
        default:
          errorMessage = err.message || 'Unknown location error'
      }

      error.value = {
        code: errorCode,
        message: errorMessage
      }

      return null
    }
  }

  // Watch position (for continuous tracking)
  const watchPosition = (options?: PositionOptions) => {
    if (!isSupported.value) {
      error.value = {
        code: 0,
        message: 'Geolocation is not supported by this browser'
      }
      return null
    }

    const watchId = navigator.geolocation.watchPosition(
      (position) => {
        coordinates.value = {
          lat: position.coords.latitude,
          lng: position.coords.longitude
        }
        permissionStatus.value = 'granted'
        error.value = null
      },
      (err) => {
        error.value = {
          code: err.code,
          message: err.message
        }
        if (err.code === 1) {
          permissionStatus.value = 'denied'
        }
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000,
        ...options
      }
    )

    return watchId
  }

  // Clear watch
  const clearWatch = (watchId: number) => {
    if (isSupported.value) {
      navigator.geolocation.clearWatch(watchId)
    }
  }

  // Check permission status (if supported)
  const checkPermission = async () => {
    if (typeof navigator !== 'undefined' && 'permissions' in navigator) {
      try {
        const permission = await navigator.permissions.query({ name: 'geolocation' as PermissionName })
        permissionStatus.value = permission.state as 'granted' | 'denied' | 'prompt'
        return permission.state
      } catch (err) {
        console.warn('Could not check geolocation permission:', err)
        permissionStatus.value = 'unknown'
        return 'unknown'
      }
    }
    return 'unknown'
  }

  // Request permission (by attempting to get position)
  const requestPermission = async () => {
    return await getCurrentPosition()
  }

  return {
    coordinates: readonly(coordinates),
    error: readonly(error),
    loading: readonly(loading),
    permissionStatus: readonly(permissionStatus),
    isSupported,
    getCurrentPosition,
    watchPosition,
    clearWatch,
    checkPermission,
    requestPermission
  }
}
