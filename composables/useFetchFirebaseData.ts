import { useStoreUtils } from "./useStoreUtils";

export async function useFetchFirebaseData<T>(url: string, params?: Record<string, any>){
    const { mapFirebaseKeyToObject } = useStoreUtils()

    try{
        // Fetch data from API
        const response: Response = await $fetch<Response>(url, { params })
        // Convert Firebase respones if applicable
        const data = mapFirebaseKeyToObject(response.body) as T[]

        return data
    }catch(error){
        console.error(`Error fetching data from ${url}:`, error)
        return []
    }
}