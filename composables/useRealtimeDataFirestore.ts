import {
  onSnapshot,
  collection,
  doc,
  DocumentReference,
  CollectionReference,
  Query,
  QuerySnapshot,
  DocumentSnapshot,
} from "firebase/firestore";

export function useFirestoreRealtime<T extends Record<string, any>>(
  pathOrRef: string | Query<T> | CollectionReference<T> | DocumentReference<T>,
  isCollection: boolean = true,
  onSucess?: (data: (T & { id: string }) | (T & { id: string })[]) => void
) {
  const { $fs } = useNuxtApp();

  const data = ref<(T & { id: string })[] | (T & { id: string }) | null>(null);
  const error = ref<Error | null>(null);
  const loading = ref(true);

  if (process.server) {
    loading.value = false;
    return { data, error, loading };
  }

  if (!$fs) {
    error.value = new Error("Firestore instance ($fs) is not available.");
    loading.value = false;
    return { data, error, loading };
  }

  const sourceRef =
    typeof pathOrRef === "string"
      ? isCollection
        ? collection($fs, pathOrRef)
        : doc($fs, pathOrRef)
      : pathOrRef;

  let unsubscribe: () => void;

  if (isCollection) {
    unsubscribe = onSnapshot(
      sourceRef as Query<T> | CollectionReference<T>,
      (snapshot: QuerySnapshot<T>) => {
        data.value = snapshot.docs.map((docSnap) => ({
          id: docSnap.id,
          ...docSnap.data(),
        })) as (T & { id: string })[];
        onSucess?.(data.value);
        loading.value = false;
      },
      (err) => {
        error.value = err;
        loading.value = false;
      }
    );
  } else {
    unsubscribe = onSnapshot(
      sourceRef as DocumentReference<T>,
      (snapshot: DocumentSnapshot<T>) => {
        if (snapshot.exists()) {
          data.value = {
            ...snapshot.data(),
            id: snapshot.id,
          } as T;
          onSucess?.(data.value);
        } else {
          data.value = null;
        }

        loading.value = false;
      },
      (err) => {
        error.value = err;
        loading.value = false;
      }
    );
  }

  onUnmounted(() => {
    console.log(`[Firestore] Unsubscribing from ${pathOrRef}`);
    unsubscribe();
  });

  return {
    data,
    error,
    loading,
  };
}
