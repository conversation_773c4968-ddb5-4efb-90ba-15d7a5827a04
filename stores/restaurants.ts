import { defineStore } from "pinia";
import type { RestaurantReview, Restaurant } from "../types";
import type { ServerResponse } from "~/types/api";

export const useRestaurantsStore = defineStore("restaurants", () => {
  const restaurants = ref<Restaurant[]>([]);
  const restaurantById = computed<Record<string, Restaurant>>(() =>
    useKeyBy(restaurants.value, "id")
  );
  // Accepts either a Timestamp, QueryDocumentSnapshot, or null
  const lastReviewSnapshot = ref<any>(null);
  const restaurantReviews = ref<RestaurantReview[]>([]);
  const hasMoreReviews = ref<boolean>(false);

  // Pagination for nearby restaurants
  const lastRestaurantDoc = ref<string | null>(null);
  const hasMoreRestaurants = ref<boolean>(false);

  // Get location store for user location
  const locationStore = useLocationStore();
  const activeRestaurant = computed<Restaurant | null>(
    () => restaurantById.value[activeRestaurantId.value] || null
  );
  const activeRestaurantId = ref<string>("");

  watch(activeRestaurant, async () => {
    if (activeRestaurant.value?.place_id) {
      console.log(
        `Fetching review for place_id: ${activeRestaurant.value.place_id}`
      );
      await fetchFirstReview();
    }
  });

  // Calculate distance between two coordinates using Haversine formula
  const calculateDistance = (
    lat1: number,
    lng1: number,
    lat2: number,
    lng2: number
  ): number => {
    const R = 6371; // Earth's radius in km
    const dLat = (lat2 - lat1) * (Math.PI / 180);
    const dLng = (lng2 - lng1) * (Math.PI / 180);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(lat1 * (Math.PI / 180)) *
        Math.cos(lat2 * (Math.PI / 180)) *
        Math.sin(dLng / 2) *
        Math.sin(dLng / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c; // Distance in km
  };

  // Calculate estimated delivery time based on distance and cuisine type
  const calculateEstimatedTime = (
    distanceKm: number,
    cuisineTypes: string[]
  ): string => {
    // Base preparation time by cuisine type (in minutes)
    const cuisinePreparationTimes: Record<string, number> = {
      japanese: 20, // Sushi, ramen - moderate prep time
      chinese: 18, // Stir-fry, quick cooking
      korean: 22, // BBQ, kimchi dishes - slightly longer
      thai: 15, // Quick stir-fry, curry
      vietnamese: 12, // Pho, fresh rolls - quick
      indian: 25, // Curry, tandoor - longer prep
      malaysian: 18, // Similar to Thai/Chinese
      indonesian: 20, // Rice dishes, satay
      filipino: 20, // Rice dishes, adobo
      singaporean: 18, // Mixed Asian styles
    };

    // Determine base preparation time
    let basePrepTime = 20; // Default preparation time
    if (cuisineTypes && cuisineTypes.length > 0) {
      const matchedCuisine = cuisineTypes.find(
        (type) => cuisinePreparationTimes[type.toLowerCase()]
      );
      if (matchedCuisine) {
        basePrepTime = cuisinePreparationTimes[matchedCuisine.toLowerCase()];
      }
    }

    // Calculate travel time (assuming average delivery speed of 25 km/h in urban areas)
    const averageDeliverySpeed = 25; // km/h
    const travelTimeMinutes = (distanceKm / averageDeliverySpeed) * 60;

    // Add buffer time for traffic, finding parking, etc.
    const bufferTime = Math.min(distanceKm * 2, 10); // 2 min per km, max 10 min

    // Total estimated time
    const totalMinutes = Math.round(
      basePrepTime + travelTimeMinutes + bufferTime
    );

    // Create time range (±5 minutes for realistic estimation)
    const minTime = Math.max(10, totalMinutes - 5); // Minimum 10 minutes
    const maxTime = totalMinutes + 5;

    return `${minTime}-${maxTime} min`;
  };

  // Sort restaurants by distance from a location
  const sortByDistance = (customLocation?: { lat: number; lng: number }) => {
    // Use custom location, user location, or fallback to Melbourne
    const location = customLocation || locationStore.currentLocation;

    restaurants.value = restaurants.value
      .map((restaurant) => {
        // Extract lat/lng from Firebase GeoPoint
        const restaurantLat = restaurant.location?.latitude || 0;
        const restaurantLng = restaurant.location?.longitude || 0;

        const distanceKm = calculateDistance(
          location.lat,
          location.lng,
          restaurantLat,
          restaurantLng
        );

        const estimatedTime = calculateEstimatedTime(
          distanceKm,
          restaurant.types
        );

        return {
          ...restaurant,
          distance: `${distanceKm.toFixed(1)} km`,
          estimatedTime,
        };
      })
      .sort((a, b) => {
        const distA = parseFloat(a.distance);
        const distB = parseFloat(b.distance);
        return distA - distB;
      });
  };

  // Computed property for restaurants sorted by distance (closest to furthest)
  const nearestRestaurant = computed(() => restaurants.value);

  // Update estimated time for a specific restaurant based on current conditions
  const updateEstimatedTime = (
    restaurantId: string,
    trafficMultiplier: number = 1
  ) => {
    const restaurant = restaurants.value.find((r) => r.id === restaurantId);
    if (restaurant && restaurant.distance) {
      const distanceKm = parseFloat(restaurant.distance.replace(" km", ""));
      const baseEstimatedTime = calculateEstimatedTime(
        distanceKm,
        restaurant.types
      );

      // Apply traffic multiplier
      const [minTime, maxTime] = baseEstimatedTime
        .split("-")
        .map((t) => parseInt(t.replace(" min", "")));
      const adjustedMinTime = Math.round(minTime * trafficMultiplier);
      const adjustedMaxTime = Math.round(maxTime * trafficMultiplier);

      restaurant.estimatedTime = `${adjustedMinTime}-${adjustedMaxTime} min`;
    }
  };

  // Bulk update estimated times for all restaurants (useful for traffic conditions)
  const updateAllEstimatedTimes = (trafficMultiplier: number = 1) => {
    restaurants.value.forEach((restaurant) => {
      if (restaurant.distance) {
        const distanceKm = parseFloat(restaurant.distance.replace(" km", ""));
        const baseEstimatedTime = calculateEstimatedTime(
          distanceKm,
          restaurant.types
        );

        // Apply traffic multiplier
        const [minTime, maxTime] = baseEstimatedTime
          .split("-")
          .map((t) => parseInt(t.replace(" min", "")));
        const adjustedMinTime = Math.round(minTime * trafficMultiplier);
        const adjustedMaxTime = Math.round(maxTime * trafficMultiplier);

        restaurant.estimatedTime = `${adjustedMinTime}-${adjustedMaxTime} min`;
      }
    });
  };

  // Get estimated time for a specific distance and cuisine (utility function)
  const getEstimatedTimeForDistance = (
    distanceKm: number,
    cuisineTypes: string[] = []
  ) => {
    return calculateEstimatedTime(distanceKm, cuisineTypes);
  };

  // Recalculate distances when user location changes
  // Note: This should only be used for restaurants that don't already have distance calculated
  const recalculateDistances = () => {
    if (restaurants.value.length > 0) {
      // Check if restaurants already have distance calculated (from API)
      const hasApiDistances = restaurants.value.some(
        (r) => r.distance && !r.distance.includes("undefined")
      );

      if (!hasApiDistances) {
        console.log(
          "Recalculating distances for restaurants without API distances"
        );
        sortByDistance();
      } else {
        console.log(
          "Skipping distance recalculation - restaurants already have API distances"
        );
      }
    }
  };

  // Get current location being used for calculations
  const getCurrentCalculationLocation = () => {
    return locationStore.currentLocation;
  };

  // Check if using user location or fallback
  const isUsingUserLocation = computed(() => {
    return locationStore.isLocationEnabled;
  });

  // --------------------- Setters ---------------------
  const setRestaurants = (newRestaurants: Restaurant[]) => {
    console.log("Setting restaurants:", newRestaurants);
    restaurants.value = newRestaurants;

    // Only sort by distance if restaurants don't already have distance calculated
    const hasApiDistances = newRestaurants.some(
      (r) => r.distance && !r.distance.includes("undefined")
    );

    if (!hasApiDistances) {
      console.log("Sorting restaurants by distance (no API distances found)");
      sortByDistance();
    } else {
      console.log(
        "Skipping distance sorting - restaurants already have API distances"
      );
    }
  };

  const setActiveRestaurantId = (restaurantId: string) => {
    activeRestaurantId.value = restaurantId;
  };

  const fetchFirstReview = async () => {
    console.log(activeRestaurant.value);
    restaurantReviews.value = [];
    try {
      const response = (await $fetch("/api/restaurant/reviews", {
        method: "GET",
        query: {
          place_id: activeRestaurant.value?.place_id,
        },
      })) as ServerResponse<RestaurantReview>;
      console.log("response:", response);
      restaurantReviews.value = response.data || [];
      lastReviewSnapshot.value = response.nextQuery || null;
      hasMoreReviews.value = response.hasMore || false;
    } catch (error) {
      console.error("Error fetching first reviews:", error);
      restaurantReviews.value = [];
    }
  };

  const fetchNextReview = async () => {
    if (!activeRestaurant.value?.id || !lastReviewSnapshot.value) return;

    try {
      const response = (await $fetch("/api/restaurant/reviews", {
        method: "GET",
        query: {
          place_id: activeRestaurant.value.place_id,
          startAfter: lastReviewSnapshot.value,
        },
      })) as {
        data: RestaurantReview[];
        hasMore: boolean;
        nextQuery: string | null;
      };

      if (response.data && response.data.length > 0) {
        restaurantReviews.value.push(...response.data);
        lastReviewSnapshot.value = response.nextQuery;
        hasMoreReviews.value = response.hasMore;
      } else {
        lastReviewSnapshot.value = null; // No more reviews
      }
      console.log(response);
    } catch (error) {
      console.error("Error fetching next reviews:", error);
    }
  };

  const fetchNearbyRestaurants = async (limit: number = 9) => {
    try {
      const userLocation = locationStore.userLocation;

      if (!userLocation?.lat || !userLocation?.lng) {
        console.warn("User location not available, using fallback location");
        // Use fallback location if user location is not available
        const fallbackLocation = locationStore.currentLocation;
        if (!fallbackLocation?.lat || !fallbackLocation?.lng) {
          console.error("No location available for nearby search");
          return;
        }
      }

      const searchLocation = userLocation || locationStore.currentLocation;
      console.log("Fetching nearby restaurants for location:", searchLocation);

      // Reset pagination for fresh fetch
      lastRestaurantDoc.value = null;

      const response = await $fetch<{
        data: Restaurant[];
        hasMore: boolean;
        lastDoc: string | null;
      }>("/api/restaurants/nearby", {
        query: {
          lat: searchLocation.lat,
          lng: searchLocation.lng,
          limit,
        },
      });

      // The API already returns restaurants sorted by distance, so we don't need to sort again
      // Just ensure they have the estimated time calculated
      const restaurantsWithEstimatedTime = (response.data || []).map(
        (restaurant) => {
          if (!restaurant.estimatedTime && restaurant.distance) {
            const distanceKm = parseFloat(
              restaurant.distance.replace(" km", "")
            );
            const estimatedTime = calculateEstimatedTime(
              distanceKm,
              restaurant.types
            );
            return { ...restaurant, estimatedTime };
          }
          return restaurant;
        }
      );

      restaurants.value = restaurantsWithEstimatedTime;
      hasMoreRestaurants.value = response.hasMore || false;
      lastRestaurantDoc.value = response.lastDoc;

      console.log(
        `Fetched ${restaurants.value.length} nearby restaurants, hasMore: ${hasMoreRestaurants.value}`
      );
      console.log(
        "First 3 restaurants distances:",
        restaurants.value
          .slice(0, 3)
          .map((r) => ({ name: r.name, distance: r.distance }))
      );
    } catch (error) {
      console.error("Error fetching nearby restaurants:", error);
      restaurants.value = [];
      hasMoreRestaurants.value = false;
      lastRestaurantDoc.value = null;
    }
  };

  const loadMoreRestaurants = async (limit: number = 9) => {
    try {
      if (!hasMoreRestaurants.value || !lastRestaurantDoc.value) {
        console.log("No more restaurants to load");
        return;
      }

      const userLocation = locationStore.userLocation;
      const searchLocation = userLocation || locationStore.currentLocation;

      if (!searchLocation?.lat || !searchLocation?.lng) {
        console.error("No location available for load more");
        return;
      }

      console.log("Loading more restaurants...");

      const response = await $fetch<{
        data: Restaurant[];
        hasMore: boolean;
        lastDoc: string | null;
      }>("/api/restaurants/nearby", {
        query: {
          lat: searchLocation.lat,
          lng: searchLocation.lng,
          limit,
          lastDoc: lastRestaurantDoc.value,
        },
      });

      // Process new restaurants with estimated time and append to existing ones
      const newRestaurantsWithEstimatedTime = (response.data || []).map(
        (restaurant) => {
          if (!restaurant.estimatedTime && restaurant.distance) {
            const distanceKm = parseFloat(
              restaurant.distance.replace(" km", "")
            );
            const estimatedTime = calculateEstimatedTime(
              distanceKm,
              restaurant.types
            );
            return { ...restaurant, estimatedTime };
          }
          return restaurant;
        }
      );

      restaurants.value.push(...newRestaurantsWithEstimatedTime);
      hasMoreRestaurants.value = response.hasMore || false;
      lastRestaurantDoc.value = response.lastDoc;

      console.log(
        `Loaded ${response.data?.length || 0} more restaurants, total: ${
          restaurants.value.length
        }, hasMore: ${hasMoreRestaurants.value}`
      );
      console.log(
        "New restaurants distances:",
        newRestaurantsWithEstimatedTime.map((r) => ({
          name: r.name,
          distance: r.distance,
        }))
      );
    } catch (error) {
      console.error("Error loading more restaurants:", error);
      hasMoreRestaurants.value = false;
    }
  };

  return {
    activeRestaurant,
    restaurants,
    nearestRestaurant,
    setRestaurants,
    setActiveRestaurantId,
    sortByDistance,
    updateEstimatedTime,
    updateAllEstimatedTimes,
    getEstimatedTimeForDistance,
    recalculateDistances,
    getCurrentCalculationLocation,
    isUsingUserLocation,

    fetchNearbyRestaurants,
    loadMoreRestaurants,
    hasMoreRestaurants,
    fetchFirstReview,
    fetchNextReview,
    restaurantReviews,
    hasMoreReviews,
  };
});
