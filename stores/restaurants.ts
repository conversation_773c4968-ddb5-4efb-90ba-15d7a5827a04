import { defineStore } from "pinia";
import type { Restaurant } from "~/types";

export const useRestaurantsStore = defineStore("restaurants", () => {
  const restaurants = ref<Restaurant[]>([]);
  const restaurantById = computed(() => useKeyBy(restaurants.value, "id"));

  // Get location store for user location
  const locationStore = useLocationStore();
  const activeRestaurant = computed(
    () => restaurantById.value[activeRestaurantId.value] || null
  );
  const activeRestaurantId = ref<string>("");

  // Calculate distance between two coordinates using Haversine formula
  const calculateDistance = (
    lat1: number,
    lng1: number,
    lat2: number,
    lng2: number
  ): number => {
    const R = 6371; // Earth's radius in km
    const dLat = (lat2 - lat1) * (Math.PI / 180);
    const dLng = (lng2 - lng1) * (Math.PI / 180);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(lat1 * (Math.PI / 180)) *
        Math.cos(lat2 * (Math.PI / 180)) *
        Math.sin(dLng / 2) *
        Math.sin(dLng / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c; // Distance in km
  };

  // Calculate estimated delivery time based on distance and cuisine type
  const calculateEstimatedTime = (
    distanceKm: number,
    cuisineTypes: string[]
  ): string => {
    // Base preparation time by cuisine type (in minutes)
    const cuisinePreparationTimes: Record<string, number> = {
      japanese: 20, // Sushi, ramen - moderate prep time
      chinese: 18, // Stir-fry, quick cooking
      korean: 22, // BBQ, kimchi dishes - slightly longer
      thai: 15, // Quick stir-fry, curry
      vietnamese: 12, // Pho, fresh rolls - quick
      indian: 25, // Curry, tandoor - longer prep
      malaysian: 18, // Similar to Thai/Chinese
      indonesian: 20, // Rice dishes, satay
      filipino: 20, // Rice dishes, adobo
      singaporean: 18, // Mixed Asian styles
    };

    // Determine base preparation time
    let basePrepTime = 20; // Default preparation time
    if (cuisineTypes && cuisineTypes.length > 0) {
      const matchedCuisine = cuisineTypes.find(
        (type) => cuisinePreparationTimes[type.toLowerCase()]
      );
      if (matchedCuisine) {
        basePrepTime = cuisinePreparationTimes[matchedCuisine.toLowerCase()];
      }
    }

    // Calculate travel time (assuming average delivery speed of 25 km/h in urban areas)
    const averageDeliverySpeed = 25; // km/h
    const travelTimeMinutes = (distanceKm / averageDeliverySpeed) * 60;

    // Add buffer time for traffic, finding parking, etc.
    const bufferTime = Math.min(distanceKm * 2, 10); // 2 min per km, max 10 min

    // Total estimated time
    const totalMinutes = Math.round(
      basePrepTime + travelTimeMinutes + bufferTime
    );

    // Create time range (±5 minutes for realistic estimation)
    const minTime = Math.max(10, totalMinutes - 5); // Minimum 10 minutes
    const maxTime = totalMinutes + 5;

    return `${minTime}-${maxTime} min`;
  };

  // Sort restaurants by distance from a location
  const sortByDistance = (customLocation?: { lat: number; lng: number }) => {
    // Use custom location, user location, or fallback to Melbourne
    const location = customLocation || locationStore.currentLocation;

    restaurants.value = restaurants.value
      .map((restaurant) => {
        const distanceKm = calculateDistance(
          location.lat,
          location.lng,
          restaurant.coordinates.lat,
          restaurant.coordinates.lng
        );

        const estimatedTime = calculateEstimatedTime(
          distanceKm,
          restaurant.types
        );

        return {
          ...restaurant,
          distance: `${distanceKm.toFixed(1)} km`,
          estimatedTime,
        };
      })
      .sort((a, b) => {
        const distA = parseFloat(a.distance);
        const distB = parseFloat(b.distance);
        return distA - distB;
      });
  };

  // Computed property for restaurants sorted by distance (closest to furthest)
  const nearestRestaurant = computed(() => restaurants.value);

  // Update estimated time for a specific restaurant based on current conditions
  const updateEstimatedTime = (
    restaurantId: string,
    trafficMultiplier: number = 1
  ) => {
    const restaurant = restaurants.value.find((r) => r.id === restaurantId);
    if (restaurant && restaurant.distance) {
      const distanceKm = parseFloat(restaurant.distance.replace(" km", ""));
      const baseEstimatedTime = calculateEstimatedTime(
        distanceKm,
        restaurant.types
      );

      // Apply traffic multiplier
      const [minTime, maxTime] = baseEstimatedTime
        .split("-")
        .map((t) => parseInt(t.replace(" min", "")));
      const adjustedMinTime = Math.round(minTime * trafficMultiplier);
      const adjustedMaxTime = Math.round(maxTime * trafficMultiplier);

      restaurant.estimatedTime = `${adjustedMinTime}-${adjustedMaxTime} min`;
    }
  };

  // Bulk update estimated times for all restaurants (useful for traffic conditions)
  const updateAllEstimatedTimes = (trafficMultiplier: number = 1) => {
    restaurants.value.forEach((restaurant) => {
      if (restaurant.distance) {
        const distanceKm = parseFloat(restaurant.distance.replace(" km", ""));
        const baseEstimatedTime = calculateEstimatedTime(
          distanceKm,
          restaurant.types
        );

        // Apply traffic multiplier
        const [minTime, maxTime] = baseEstimatedTime
          .split("-")
          .map((t) => parseInt(t.replace(" min", "")));
        const adjustedMinTime = Math.round(minTime * trafficMultiplier);
        const adjustedMaxTime = Math.round(maxTime * trafficMultiplier);

        restaurant.estimatedTime = `${adjustedMinTime}-${adjustedMaxTime} min`;
      }
    });
  };

  // Get estimated time for a specific distance and cuisine (utility function)
  const getEstimatedTimeForDistance = (
    distanceKm: number,
    cuisineTypes: string[] = []
  ) => {
    return calculateEstimatedTime(distanceKm, cuisineTypes);
  };

  // Recalculate distances when user location changes
  const recalculateDistances = () => {
    if (restaurants.value.length > 0) {
      sortByDistance();
    }
  };

  // Get current location being used for calculations
  const getCurrentCalculationLocation = () => {
    return locationStore.currentLocation;
  };

  // Check if using user location or fallback
  const isUsingUserLocation = computed(() => {
    return locationStore.isLocationEnabled;
  });

  // --------------------- Setters ---------------------
  const setRestaurants = (newRestaurants: Restaurant[]) => {
    console.log("Setting restaurants:", newRestaurants);
    restaurants.value = newRestaurants;
    sortByDistance();
  };

  const setActiveRestaurantId = (restaurantId: string) => {
    activeRestaurantId.value = restaurantId;
  };

  return {
    activeRestaurant,
    restaurants,
    nearestRestaurant,
    setRestaurants,
    setActiveRestaurantId,
    sortByDistance,
    updateEstimatedTime,
    updateAllEstimatedTimes,
    getEstimatedTimeForDistance,
    recalculateDistances,
    getCurrentCalculationLocation,
    isUsingUserLocation,
  };
});
