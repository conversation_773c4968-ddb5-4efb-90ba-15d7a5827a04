import { defineStore } from 'pinia'
import type { GeolocationCoordinates } from '~/composables/useGeolocation'

export interface LocationState {
  userLocation: GeolocationCoordinates | null
  isLocationEnabled: boolean
  locationError: string | null
  isLoading: boolean
  hasAskedPermission: boolean
  fallbackLocation: GeolocationCoordinates
}

export const useLocationStore = defineStore('location', () => {
  // Default fallback location (Melbourne)
  const MELBOURNE_LOCATION: GeolocationCoordinates = {
    lat: -37.840935,
    lng: 144.946457,
  }

  // State
  const userLocation = ref<GeolocationCoordinates | null>(null)
  const isLocationEnabled = ref(false)
  const locationError = ref<string | null>(null)
  const isLoading = ref(false)
  const hasAskedPermission = ref(false)
  const fallbackLocation = ref<GeolocationCoordinates>(MELBOURNE_LOCATION)

  // Composable for geolocation
  const { getCurrentPosition, checkPermission, isSupported } = useGeolocation()

  // Computed properties
  const currentLocation = computed(() => {
    return userLocation.value || fallbackLocation.value
  })

  const locationStatus = computed(() => {
    if (isLoading.value) return 'loading'
    if (locationError.value) return 'error'
    if (userLocation.value) return 'user-location'
    return 'fallback-location'
  })

  const locationDisplayName = computed(() => {
    switch (locationStatus.value) {
      case 'loading':
        return 'Getting your location...'
      case 'error':
        return 'Using default location (Melbourne)'
      case 'user-location':
        return 'Using your current location'
      case 'fallback-location':
        return 'Using default location (Melbourne)'
      default:
        return 'Location unknown'
    }
  })

  // Actions
  const requestUserLocation = async (showPrompt = true) => {
    if (!isSupported.value) {
      locationError.value = 'Geolocation not supported'
      return false
    }

    isLoading.value = true
    locationError.value = null
    hasAskedPermission.value = true

    try {
      const coords = await getCurrentPosition()
      
      if (coords) {
        userLocation.value = coords
        isLocationEnabled.value = true
        locationError.value = null
        
        // Store in localStorage for future visits
        if (typeof localStorage !== 'undefined') {
          localStorage.setItem('userLocation', JSON.stringify(coords))
          localStorage.setItem('locationEnabled', 'true')
        }
        
        return true
      } else {
        isLocationEnabled.value = false
        return false
      }
    } catch (error: any) {
      locationError.value = error.message || 'Failed to get location'
      isLocationEnabled.value = false
      return false
    } finally {
      isLoading.value = false
    }
  }

  const setFallbackLocation = (coords: GeolocationCoordinates) => {
    fallbackLocation.value = coords
  }

  const clearUserLocation = () => {
    userLocation.value = null
    isLocationEnabled.value = false
    locationError.value = null
    
    // Clear from localStorage
    if (typeof localStorage !== 'undefined') {
      localStorage.removeItem('userLocation')
      localStorage.removeItem('locationEnabled')
    }
  }

  const loadStoredLocation = () => {
    if (typeof localStorage !== 'undefined') {
      try {
        const storedLocation = localStorage.getItem('userLocation')
        const storedEnabled = localStorage.getItem('locationEnabled')
        
        if (storedLocation && storedEnabled === 'true') {
          const coords = JSON.parse(storedLocation)
          userLocation.value = coords
          isLocationEnabled.value = true
        }
      } catch (error) {
        console.warn('Failed to load stored location:', error)
      }
    }
  }

  const checkLocationPermission = async () => {
    try {
      const permission = await checkPermission()
      
      switch (permission) {
        case 'granted':
          // If permission is granted but we don't have location, try to get it
          if (!userLocation.value) {
            await requestUserLocation(false)
          }
          break
        case 'denied':
          locationError.value = 'Location access denied'
          isLocationEnabled.value = false
          break
        case 'prompt':
          // Permission not yet requested
          break
      }
      
      return permission
    } catch (error) {
      console.warn('Failed to check location permission:', error)
      return 'unknown'
    }
  }

  // Initialize location on store creation
  const initializeLocation = async () => {
    // First, try to load from localStorage
    loadStoredLocation()
    
    // Then check current permission status
    await checkLocationPermission()
  }

  return {
    // State
    userLocation: readonly(userLocation),
    isLocationEnabled: readonly(isLocationEnabled),
    locationError: readonly(locationError),
    isLoading: readonly(isLoading),
    hasAskedPermission: readonly(hasAskedPermission),
    fallbackLocation: readonly(fallbackLocation),
    
    // Computed
    currentLocation,
    locationStatus,
    locationDisplayName,
    
    // Actions
    requestUserLocation,
    setFallbackLocation,
    clearUserLocation,
    loadStoredLocation,
    checkLocationPermission,
    initializeLocation,
  }
})
