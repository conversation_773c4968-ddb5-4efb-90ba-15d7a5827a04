<template>
  <div class="restaurant-details">
    <!-- Loading State -->
    <!-- <div v-if="loading" class="restaurant-details__loading">
      <div class="container">
        <div class="restaurant-details__skeleton">
          <div class="skeleton skeleton--hero"></div>
          <div class="skeleton skeleton--title"></div>
          <div class="skeleton skeleton--text"></div>
          <div class="skeleton skeleton--text"></div>
        </div>
      </div>
    </div> -->

    <!-- Not Found State -->
    <div v-if="!activeRestaurant" class="restaurant-details__not-found">
      <div class="container">
        <ErrorState type="404" title="Restaurant Not Found"
          message="Sorry, we couldn't find the restaurant you're looking for."
          details="The restaurant may have been removed or you may have entered an incorrect URL." :show-retry="false"
          :show-go-back="true" :show-go-home="true" back-url="/browse" @go-back="navigateTo('/browse')"
          @go-home="navigateTo('/')" />
      </div>
    </div>

    <!-- Error State -->
    <!-- <ErrorState v-else-if="error" type="404" title="Restaurant Not Found"
      message="Sorry, we couldn't find the restaurant you're looking for."
      details="The restaurant may have been removed or you may have entered an incorrect URL." :show-retry="false"
      :show-go-back="true" :show-go-home="true" back-url="/browse" @go-back="navigateTo('/browse')"
      @go-home="navigateTo('/')" /> -->

    <!-- Restaurant Content -->
    <div v-else-if="activeRestaurant" class="restaurant-details__content">
      <!-- Hero Section -->
      <section class="restaurant-hero">
        <div class="restaurant-hero__image">
          <img :src="heroBackgroundImage" :alt="activeRestaurant.businessInfo?.name || activeRestaurant.name"
            class="restaurant-hero__img" />
          <div class="restaurant-hero__overlay">
            <div class="container">
              <div class="restaurant-hero__content">
                <button @click="goBack" class="restaurant-hero__back-btn">
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path d="m15 18-6-6 6-6" />
                  </svg>
                  Back
                </button>

                <div class="restaurant-hero__info">
                  <h1 class="restaurant-hero__name">{{ activeRestaurant.businessInfo?.name || activeRestaurant.name }}
                  </h1>
                  <div class="restaurant-hero__meta">
                    <StarRating :rating="activeRestaurant.rating" :review-count="99" size="large" />
                    <div class="restaurant-hero__badges">
                      <span v-for="type in activeRestaurant.types?.slice(0, 3)" :key="type"
                        class="restaurant-hero__badge">
                        {{ type }}
                      </span>
                    </div>
                  </div>

                  <div class="restaurant-hero__details">
                    <div class="restaurant-hero__detail" v-if="activeRestaurant.distance">
                      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <circle cx="12" cy="12" r="10" />
                        <path d="M12 6v6l4 2" />
                      </svg>
                      {{ activeRestaurant.distance }}
                    </div>

                    <div class="restaurant-hero__detail" v-if="activeRestaurant.estimatedTime">
                      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <circle cx="12" cy="12" r="10" />
                        <polyline points="12,6 12,12 16,14" />
                      </svg>
                      {{ activeRestaurant.estimatedTime }}
                    </div>

                    <div class="restaurant-hero__detail">
                      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
                      </svg>
                      $$
                    </div>
                  </div>

                  <!-- Quick Actions -->
                  <div class="restaurant-hero__actions">
                    <!-- <button class="restaurant-hero__action-btn" @click="getDirections" v-if="restaurant.coordinates">
                      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" />
                        <circle cx="12" cy="10" r="3" />
                      </svg>
                      Get Directions
                    </button> -->

                    <!-- <button class="restaurant-hero__action-btn" @click="openGooglePage"
                      v-if="restaurant.businessInfo?.googleUrl">
                      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6" />
                        <polyline points="15,3 21,3 21,9" />
                        <line x1="10" y1="14" x2="21" y2="3" />
                      </svg>
                      View on Google
                    </button> -->

                    <!-- <button class="restaurant-hero__action-btn" @click="viewMenu" v-if="restaurant.menuUrl">
                      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
                        <polyline points="14,2 14,8 20,8" />
                        <line x1="16" y1="13" x2="8" y2="13" />
                        <line x1="16" y1="17" x2="8" y2="17" />
                        <polyline points="10,9 9,9 8,9" />
                      </svg>
                      View Menu
                    </button> -->

                    <!-- <button class="restaurant-hero__action-btn" @click="openWebsite"
                      v-if="restaurant.businessInfo?.website">
                      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <circle cx="12" cy="12" r="10" />
                        <path d="M2 12h20" />
                        <path
                          d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z" />
                      </svg>
                      Website
                    </button> -->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Main Content -->
      <section class="restaurant-main">
        <div class="container">
          <div class="restaurant-main__layout">
            <!-- Left Column -->
            <div class="restaurant-main__content">
              <!-- About Section -->
              <div class="restaurant-section">
                <h2 class="restaurant-section__title">About</h2>
                <div class="restaurant-section__content">
                  <p class="restaurant-about__description">
                    {{ activeRestaurant.businessInfo?.summary ||
                      `Experience authentic ${activeRestaurant.types?.[0] || 'Asian'} cuisine at
                    ${activeRestaurant.businessInfo?.name || activeRestaurant.name}. Our skilled chefs prepare
                    traditional dishes
                    using fresh ingredients and
                    time-honored recipes, bringing you the true flavors of Asia.` }}
                  </p>

                  <div class="restaurant-about__info">
                    <div class="restaurant-about__item">
                      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" />
                        <circle cx="12" cy="10" r="3" />
                      </svg>
                      <div>
                        <strong>Address</strong>
                        <div class="restaurant-address">
                          <p class="restaurant-address__text">{{ activeRestaurant.address }}</p>
                          <div class="restaurant-address__actions">
                            <a :href="activeRestaurant.businessInfo?.googleUrl" target="_blank"
                              rel="noopener noreferrer" v-if="activeRestaurant.businessInfo?.googleUrl"
                              class="restaurant-address__btn restaurant-address__btn--google">
                              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6" />
                                <polyline points="15,3 21,3 21,9" />
                                <line x1="10" y1="14" x2="21" y2="3" />
                              </svg>
                              View on Google
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="restaurant-about__item" v-if="activeRestaurant.businessInfo?.website">
                      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <circle cx="12" cy="12" r="10" />
                        <path d="M2 12h20" />
                        <path
                          d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z" />
                      </svg>
                      <div>
                        <strong>Website</strong>
                        <p>
                          <a :href="activeRestaurant.businessInfo.website" target="_blank" rel="noopener noreferrer"
                            class="restaurant-about__link">
                            {{ activeRestaurant.businessInfo.website }}
                          </a>
                        </p>
                      </div>
                    </div>



                    <!-- <div class="restaurant-about__item" v-if="restaurant.businessInfo?.googleUrl">
                      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6" />
                        <polyline points="15,3 21,3 21,9" />
                        <line x1="10" y1="14" x2="21" y2="3" />
                      </svg>
                      <div>
                        <strong>Google Business</strong>
                        <p>
                          <a :href="restaurant.businessInfo.googleUrl" target="_blank" rel="noopener noreferrer"
                            class="restaurant-about__link">
                            View on Google Maps
                          </a>
                        </p>
                      </div>
                    </div> -->
                  </div>
                </div>
              </div>

              <!-- Photo Gallery Section -->
              <div class="restaurant-section" v-if="restaurantPhotos.length > 0">
                <h2 class="restaurant-section__title">Photos</h2>
                <div class="restaurant-section__content">
                  <div class="photo-gallery">
                    <div class="photo-gallery__main">
                      <div class="photo-gallery__main-image">
                        <img :src="restaurantPhotos[currentPhotoIndex].downloadUrl"
                          :alt="`${activeRestaurant.businessInfo?.name || activeRestaurant.name} photo ${currentPhotoIndex + 1}`" />

                        <!-- Navigation arrows -->
                        <button v-if="restaurantPhotos.length > 1" class="photo-gallery__nav photo-gallery__nav--prev"
                          @click="previousPhoto">
                          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <polyline points="15,18 9,12 15,6" />
                          </svg>
                        </button>
                        <button v-if="restaurantPhotos.length > 1" class="photo-gallery__nav photo-gallery__nav--next"
                          @click="nextPhoto">
                          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <polyline points="9,18 15,12 9,6" />
                          </svg>
                        </button>

                        <!-- Photo counter -->
                        <div class="photo-gallery__counter" v-if="restaurantPhotos.length > 1">
                          {{ currentPhotoIndex + 1 }} / {{ restaurantPhotos.length }}
                        </div>
                      </div>
                    </div>

                    <!-- Thumbnail strip -->
                    <div class="photo-gallery__thumbnails" v-if="restaurantPhotos.length > 1">
                      <button v-for="(photo, index) in restaurantPhotos" :key="photo.photo_id"
                        class="photo-gallery__thumbnail"
                        :class="{ 'photo-gallery__thumbnail--active': index === currentPhotoIndex }"
                        @click="currentPhotoIndex = index">
                        <img :src="photo.downloadUrl"
                          :alt="`${activeRestaurant.businessInfo?.name || activeRestaurant.name} thumbnail ${index + 1}`" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Menu Section -->
              <div class="restaurant-section" v-if="activeRestaurant.menuUrl">
                <h2 class="restaurant-section__title">Menu</h2>
                <div class="restaurant-section__content">
                  <div class="restaurant-menu-link">
                    <div class="restaurant-menu-link__content">
                      <div class="restaurant-menu-link__icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
                          <polyline points="14,2 14,8 20,8" />
                          <line x1="16" y1="13" x2="8" y2="13" />
                          <line x1="16" y1="17" x2="8" y2="17" />
                          <polyline points="10,9 9,9 8,9" />
                        </svg>
                      </div>
                      <div class="restaurant-menu-link__text">
                        <h3>View Full Menu</h3>
                        <p>Check out our complete menu with prices and descriptions</p>
                      </div>
                    </div>
                    <a :href="activeRestaurant.menuUrl" target="_blank" rel="noopener noreferrer"
                      class="restaurant-menu-link__btn">
                      <span>Open Menu</span>
                      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6" />
                        <polyline points="15,3 21,3 21,9" />
                        <line x1="10" y1="14" x2="21" y2="3" />
                      </svg>
                    </a>
                  </div>
                </div>
              </div>

              <!-- Reviews Section -->
              <div class="restaurant-section">
                <h2 class="restaurant-section__title">Reviews</h2>
                <div class="restaurant-section__content">
                  <RestaurantReviews :restaurant="activeRestaurant" />
                </div>
              </div>
            </div>

            <!-- Right Sidebar -->
            <div class="restaurant-main__sidebar">
              <RestaurantSidebar :restaurant="activeRestaurant" />
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
// Get restaurant ID from route
const route = useRoute()
const restaurantId = route.params.id as string
const restaurantsStore = useRestaurantsStore()
const { activeRestaurant } = storeToRefs(restaurantsStore)

onMounted(() => {
  restaurantsStore.setActiveRestaurantId(restaurantId)
})

watch(async () => restaurantId, async (newId) => {
  if (!newId) return
  console.log('Fetching restaurant with ID:', newId)
  await restaurantsStore.fetchFirstReview()
}, { immediate: true })
// Set page meta
useHead({
  title: computed(() => activeRestaurant.value ? `${activeRestaurant.value.name} - Asian Tables` : 'Restaurant - Asian Tables'),
  meta: [
    {
      name: 'description',
      content: computed(() => activeRestaurant.value ?
        `Discover ${activeRestaurant.value.name}, a ${activeRestaurant.value.types?.[0] || 'Asian'} restaurant. View menu, read reviews, and get directions.` :
        'Restaurant details and menu'
      )
    }
  ]
})
// onMounted(async () => {
//   await restaurantsStore.fetchFirstReview()
// })


// Get restaurants from store

// watch(() => activeRestaurant, async () => {
//   if (!activeRestaurant.value) return
//   restaurantsStore.setActiveRestaurantId(restaurantId)
//   console.log('Fetching first review for restaurant:', restaurantId)
//   await restaurantsStore.fetchFirstReview()
//   }, { immediate: true })

// Photo gallery state
const currentPhotoIndex = ref(0)

// Computed properties for photos
const restaurantPhotos = computed(() => {
  if (activeRestaurant.value?.photos && Array.isArray(activeRestaurant.value.photos)) {
    // Filter out any non-object items and ensure they have downloadUrl
    return activeRestaurant.value.photos.filter((photo: any) =>
      typeof photo === 'object' && photo !== null && photo.downloadUrl
    )
  }
  return []
})

const heroBackgroundImage = computed(() => {
  // Find the cover photo from the photos array
  const coverPhoto = restaurantPhotos.value.find((photo: any) => photo.isCover === true)

  if (coverPhoto?.downloadUrl) {
    return coverPhoto.downloadUrl
  }

  // Fallback to first photo if no cover photo
  if (restaurantPhotos.value.length > 0) {
    return restaurantPhotos.value[0].downloadUrl
  }

  // Final fallback to placeholder
  return '/images/placeholder-restaurant.jpg'
})

// Photo gallery navigation
const nextPhoto = () => {
  if (restaurantPhotos.value.length > 1) {
    currentPhotoIndex.value = (currentPhotoIndex.value + 1) % restaurantPhotos.value.length
  }
}

const previousPhoto = () => {
  if (restaurantPhotos.value.length > 1) {
    currentPhotoIndex.value = currentPhotoIndex.value === 0
      ? restaurantPhotos.value.length - 1
      : currentPhotoIndex.value - 1
  }
}



// Navigation
const goBack = () => {
  navigateTo('/browse')
}

// Action handlers
const getDirections = () => {
  if (activeRestaurant.value?.coordinates) {
    const { lat, lng } = activeRestaurant.value.coordinates
    const url = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`
    window.open(url, '_blank')
  }
}

const openGooglePage = () => {
  if (activeRestaurant.value?.businessInfo?.googleUrl) {
    window.open(activeRestaurant.value.businessInfo.googleUrl, '_blank')
  }
}

const viewMenu = () => {
  if (activeRestaurant.value?.menuUrl) {
    window.open(activeRestaurant.value.menuUrl, '_blank')
  }
}

const openWebsite = () => {
  if (activeRestaurant.value?.businessInfo?.website) {
    window.open(activeRestaurant.value.businessInfo.website, '_blank')
  }
}
</script>

<style lang="scss" scoped>
.restaurant-details {
  min-height: 100vh;

  &__loading {
    min-height: 50vh;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__skeleton {
    max-width: 800px;
    width: 100%;

    .skeleton {
      background: linear-gradient(90deg, $bg-secondary 25%, $border-light 50%, $bg-secondary 75%);
      background-size: 200% 100%;
      animation: loading 1.5s infinite;
      border-radius: $border-radius-md;

      &--hero {
        height: 300px;
        margin-bottom: $spacing-lg;
      }

      &--title {
        height: 40px;
        width: 60%;
        margin-bottom: $spacing-md;
      }

      &--text {
        height: 20px;
        margin-bottom: $spacing-sm;

        &:last-child {
          width: 40%;
        }
      }
    }
  }
}

.restaurant-hero {
  position: relative;
  height: 60vh;
  min-height: 400px;
  overflow: hidden;

  @media (max-width: $tablet) {
    height: 50vh;
    min-height: 300px;
  }

  &__image {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }

  &__img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  &__overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom,
        rgba(0, 0, 0, 0.3) 0%,
        rgba(0, 0, 0, 0.1) 50%,
        rgba(0, 0, 0, 0.8) 100%);
    display: flex;
    align-items: flex-end;
  }

  &__content {
    width: 100%;
    color: $white;
    padding: $spacing-xxl $spacing-lg;

    @media (max-width: $tablet) {
      padding: $spacing-xl $spacing-md;
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
    }
  }

  &__back-btn {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: $white;
    padding: $spacing-sm $spacing-md;
    border-radius: $border-radius-lg;
    font-family: $font-secondary;
    font-weight: $font-medium;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: $spacing-lg;

    svg {
      width: 16px;
      height: 16px;
    }

    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }

  &__name {
    font-family: $font-primary;
    font-size: $font-4xl;
    font-weight: $font-bold;
    margin: 0 0 $spacing-md 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);

    @media (max-width: $tablet) {
      font-size: $font-3xl;
    }

    @media (max-width: $mobile) {
      font-size: $font-2xl;
    }
  }

  &__meta {
    display: flex;
    flex-direction: column;
    gap: $spacing-md;
    margin-bottom: $spacing-lg;

    @media (max-width: $tablet) {
      gap: $spacing-sm;
    }
  }

  &__badges {
    display: flex;
    gap: $spacing-sm;
    flex-wrap: wrap;
  }

  &__badge {
    padding: $spacing-xs $spacing-sm;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: $border-radius-sm;
    font-family: $font-secondary;
    font-size: $font-xs;
    font-weight: $font-medium;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  &__details {
    display: flex;
    gap: $spacing-lg;
    flex-wrap: wrap;

    @media (max-width: $mobile) {
      gap: $spacing-md;
    }
  }

  &__detail {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    font-family: $font-secondary;
    font-size: $font-sm;
    font-weight: $font-medium;

    svg {
      width: 16px;
      height: 16px;
      opacity: 0.8;
    }
  }

  &__actions {
    display: flex;
    gap: $spacing-sm;
    margin-top: $spacing-lg;
    flex-wrap: wrap;

    @media (max-width: $mobile) {
      flex-direction: column;
      gap: $spacing-xs;
    }
  }

  &__action-btn {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    padding: $spacing-sm $spacing-md;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: $white;
    border-radius: $border-radius-lg;
    font-family: $font-secondary;
    font-size: $font-sm;
    font-weight: $font-medium;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;

    svg {
      width: 16px;
      height: 16px;
    }

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      border-color: rgba(255, 255, 255, 0.5);
      transform: translateY(-2px);
    }

    @media (max-width: $mobile) {
      justify-content: center;
      padding: $spacing-md;
    }
  }
}

.restaurant-main {
  padding: $spacing-xxl 0;

  @media (max-width: $tablet) {
    padding: $spacing-xl 0;
  }

  .container {
    max-width: $extra-large-desktop; // Larger than standard $large-desktop (1200px)
    margin: 0 auto;
    padding: 0 $spacing-lg;

    @media (max-width: $tablet) {
      padding: 0 $spacing-md;
    }
  }

  &__layout {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: $spacing-xxl;
    align-items: start;

    @media (max-width: $desktop) {
      grid-template-columns: 1fr 320px;
      gap: $spacing-xl;
    }

    @media (max-width: $tablet) {
      grid-template-columns: 1fr;
      gap: $spacing-xl;
    }
  }

  &__content {
    min-width: 0;
  }

  &__sidebar {
    @media (max-width: $tablet) {
      order: -1;
    }
  }
}

.restaurant-section {
  margin-bottom: $spacing-xxl;

  &:last-child {
    margin-bottom: 0;
  }

  &__title {
    font-family: $font-primary;
    font-size: $font-2xl;
    font-weight: $font-bold;
    color: $text-primary;
    margin: 0 0 $spacing-lg 0;
    padding-bottom: $spacing-sm;
    border-bottom: 2px solid $primary-orange;
    display: flex;
    align-items: center;
    gap: $spacing-sm;

    @media (max-width: $tablet) {
      font-size: $font-xl;
    }

    &::before {
      content: '';
      width: 4px;
      height: 24px;
      background: $primary-orange;
      border-radius: 2px;
    }
  }
}

.restaurant-about {
  &__description {
    font-family: $font-secondary;
    font-size: $font-base;
    line-height: $leading-relaxed;
    color: $text-primary;
    margin: 0 0 $spacing-xl 0;
  }

  &__info {
    display: flex;
    flex-direction: column;
    gap: $spacing-lg;
  }

  &__item {
    display: flex;
    gap: $spacing-md;
    padding: $spacing-lg;
    background: $bg-secondary;
    border-radius: $border-radius-lg;

    svg {
      width: 24px;
      height: 24px;
      color: $primary-orange;
      flex-shrink: 0;
      margin-top: 2px;
    }

    div {
      flex: 1;

      strong {
        font-family: $font-secondary;
        font-weight: $font-semibold;
        color: $text-primary;
        display: block;
        margin-bottom: $spacing-xs;
      }

      p {
        font-family: $font-secondary;
        color: $text-secondary;
        margin: 0;
        line-height: $leading-relaxed;
      }
    }
  }

  &__link {
    color: $primary-orange;
    text-decoration: none;
    transition: color 0.3s ease;

    &:hover {
      color: color.adjust($primary-orange, $lightness: -10%);
      text-decoration: underline;
    }
  }
}

.restaurant-address {
  &__text {
    margin: 0 0 $spacing-sm 0;
    font-family: $font-secondary;
    font-size: $font-sm;
    color: $text-secondary;
    line-height: $leading-relaxed;
  }

  &__actions {
    display: flex;
    gap: $spacing-xs;
    flex-wrap: wrap;
  }

  &__btn {
    display: inline-flex;
    align-items: center;
    gap: $spacing-xs;
    padding: $spacing-xs $spacing-sm;
    border-radius: $border-radius-md;
    font-family: $font-secondary;
    font-size: $font-xs;
    font-weight: $font-medium;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;

    svg {
      width: 14px;
      height: 14px;
    }

    &--directions {
      background: $primary-orange;
      color: $white;

      &:hover {
        background: color.adjust($primary-orange, $lightness: -10%);
        transform: translateY(-1px);
      }
    }

    &--google {
      background: $bg-secondary;
      color: $text-secondary;
      border: 1px solid $border-light;

      &:hover {
        background: $white;
        color: $primary-orange;
        border-color: $primary-orange;
        transform: translateY(-1px);
      }
    }

    @media (max-width: $mobile) {
      padding: $spacing-sm;
      font-size: $font-xs;
    }
  }
}

.restaurant-features {
  &__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: $spacing-md;

    @media (max-width: $mobile) {
      grid-template-columns: 1fr;
    }
  }

  &__item {
    display: flex;
    align-items: center;
    gap: $spacing-sm;
    padding: $spacing-md;
    background: $bg-secondary;
    border-radius: $border-radius-lg;
    border: 1px solid $border-light;
  }

  &__icon {
    width: 24px;
    height: 24px;
    color: $primary-orange;
    flex-shrink: 0;

    svg {
      width: 100%;
      height: 100%;
    }
  }

  &__text {
    font-family: $font-secondary;
    font-size: $font-sm;
    font-weight: $font-medium;
    color: $text-primary;
  }
}

.restaurant-menu-link {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-xl;
  background: linear-gradient(135deg, $bg-secondary 0%, $white 100%);
  border-radius: $border-radius-xl;
  border: 1px solid $border-light;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: $shadow-medium;
    border-color: $primary-orange;
  }

  @media (max-width: $tablet) {
    flex-direction: column;
    gap: $spacing-lg;
    text-align: center;
  }

  &__content {
    display: flex;
    align-items: center;
    gap: $spacing-lg;

    @media (max-width: $tablet) {
      flex-direction: column;
      gap: $spacing-md;
    }
  }

  &__icon {
    width: 48px;
    height: 48px;
    background: $primary-orange;
    color: $white;
    border-radius: $border-radius-lg;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    svg {
      width: 24px;
      height: 24px;
    }
  }

  &__text {
    h3 {
      font-family: $font-primary;
      font-size: $font-lg;
      font-weight: $font-semibold;
      color: $text-primary;
      margin: 0 0 $spacing-xs 0;
    }

    p {
      font-family: $font-secondary;
      font-size: $font-sm;
      color: $text-secondary;
      margin: 0;
    }
  }

  &__btn {
    display: flex;
    align-items: center;
    gap: $spacing-sm;
    padding: $spacing-md $spacing-lg;
    background: linear-gradient(135deg, $primary-red 0%, $primary-orange 100%);
    color: $white;
    border: none;
    border-radius: $border-radius-lg;
    font-family: $font-secondary;
    font-size: $font-sm;
    font-weight: $font-semibold;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;

    svg {
      width: 16px;
      height: 16px;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: $shadow-medium;
    }
  }
}

// Photo Gallery Styles
.photo-gallery {
  &__main {
    margin-bottom: $spacing-lg;
  }

  &__main-image {
    position: relative;
    width: 100%;
    height: 400px;
    border-radius: $border-radius-xl;
    overflow: hidden;
    background: #1a1a1a;
    display: flex;
    align-items: center;
    justify-content: center;

    @media (max-width: $tablet) {
      height: 300px;
    }

    @media (max-width: $mobile) {
      height: 250px;
    }

    img {
      max-width: 100%;
      max-height: 100%;
      width: auto;
      height: auto;
      object-fit: contain;
      transition: transform 0.3s ease;
    }

    &:hover img {
      transform: scale(1.02);
    }
  }

  &__nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 48px;
    height: 48px;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(10px);
    border: none;
    border-radius: 50%;
    color: $white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 2;

    svg {
      width: 20px;
      height: 20px;
    }

    &:hover {
      background: rgba(0, 0, 0, 0.9);
      transform: translateY(-50%) scale(1.1);
    }

    &--prev {
      left: $spacing-md;
    }

    &--next {
      right: $spacing-md;
    }

    @media (max-width: $mobile) {
      width: 40px;
      height: 40px;

      &--prev {
        left: $spacing-sm;
      }

      &--next {
        right: $spacing-sm;
      }

      svg {
        width: 16px;
        height: 16px;
      }
    }
  }

  &__counter {
    position: absolute;
    bottom: $spacing-md;
    right: $spacing-md;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(10px);
    color: $white;
    padding: $spacing-xs $spacing-sm;
    border-radius: $border-radius-lg;
    font-family: $font-secondary;
    font-size: $font-sm;
    font-weight: $font-medium;

    @media (max-width: $mobile) {
      bottom: $spacing-sm;
      right: $spacing-sm;
      font-size: $font-xs;
    }
  }

  &__thumbnails {
    display: flex;
    gap: $spacing-sm;
    overflow-x: auto;
    padding: $spacing-sm 0;
    scrollbar-width: thin;
    scrollbar-color: $border-light transparent;

    &::-webkit-scrollbar {
      height: 4px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: $border-light;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: $primary-orange;
    }
  }

  &__thumbnail {
    flex-shrink: 0;
    width: 80px;
    height: 60px;
    border-radius: $border-radius-md;
    overflow: hidden;
    border: 2px solid transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    background: none;
    padding: 0;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    &:hover {
      border-color: $primary-orange;
      transform: scale(1.05);
    }

    &--active {
      border-color: $primary-orange;
      box-shadow: 0 0 0 2px rgba($primary-orange, 0.3);
    }

    @media (max-width: $mobile) {
      width: 60px;
      height: 45px;
    }
  }
}



@keyframes loading {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}
</style>
