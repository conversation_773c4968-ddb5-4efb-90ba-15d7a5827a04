<template>
  <div class="restaurant-details">
    <!-- Not Found State -->
    <div v-if="!activeRestaurant" class="restaurant-details__not-found">
      <div class="container">
        <ErrorState type="404" title="Restaurant Not Found"
          message="Sorry, we couldn't find the restaurant you're looking for."
          details="The restaurant may have been removed or you may have entered an incorrect URL." :show-retry="false"
          :show-go-back="true" :show-go-home="true" back-url="/browse" @go-back="navigateTo('/browse')"
          @go-home="navigateTo('/')" />
      </div>
    </div>
    <!-- Restaurant Content -->
    <div v-else-if="activeRestaurant" class="restaurant-details__content">
      <RestaurantHero />
      <RestaurantContent />
    </div>
  </div>
</template>

<script setup lang="ts">
// Get restaurant ID from route
const route = useRoute()
const restaurantId = route.params.id as string
const restaurantsStore = useRestaurantsStore()
const { activeRestaurant } = storeToRefs(restaurantsStore)

onMounted(() => {
  restaurantsStore.setActiveRestaurantId(restaurantId)
})
useHead({
  title: computed(() => activeRestaurant.value ? `${activeRestaurant.value.name} - Asian Tables` : 'Restaurant - Asian Tables'),
  meta: [
    {
      name: 'description',
      content: computed(() => activeRestaurant.value ?
        `Discover ${activeRestaurant.value.name}, a ${activeRestaurant.value.types?.[0] || 'Asian'} restaurant. View menu, read reviews, and get directions.` :
        'Restaurant details and menu'
      )
    }
  ]
})


</script>

<style lang="scss" scoped>
@keyframes loading {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}
</style>
