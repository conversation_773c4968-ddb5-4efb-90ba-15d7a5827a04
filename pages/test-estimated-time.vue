<template>
  <div class="test-page">
    <div class="container">
      <h1>Estimated Time Calculation Test</h1>
      
      <div class="test-section">
        <h2>Test Distance & Cuisine Combinations</h2>
        <div class="test-grid">
          <div v-for="test in testCases" :key="test.id" class="test-card">
            <h3>{{ test.name }}</h3>
            <p><strong>Distance:</strong> {{ test.distance }} km</p>
            <p><strong>Cuisine:</strong> {{ test.cuisine.join(', ') }}</p>
            <p><strong>Estimated Time:</strong> 
              <span class="estimated-time">{{ test.estimatedTime }}</span>
            </p>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h2>Traffic Condition Simulator</h2>
        <div class="traffic-controls">
          <label>Traffic Multiplier: {{ trafficMultiplier }}x</label>
          <input 
            type="range" 
            v-model="trafficMultiplier" 
            min="0.5" 
            max="3" 
            step="0.1"
            class="traffic-slider"
          />
          <div class="traffic-labels">
            <span>Light Traffic (0.5x)</span>
            <span>Normal (1x)</span>
            <span>Heavy Traffic (3x)</span>
          </div>
        </div>
        
        <button @click="updateTrafficConditions" class="update-btn">
          Update All Restaurant Times
        </button>
      </div>

      <div class="test-section">
        <h2>Live Restaurant Data</h2>
        <div class="restaurant-list">
          <div v-for="restaurant in restaurants" :key="restaurant.id" class="restaurant-item">
            <h4>{{ restaurant.name }}</h4>
            <div class="restaurant-details">
              <span class="distance">📍 {{ restaurant.distance }}</span>
              <span class="time">⏱️ {{ restaurant.estimatedTime }}</span>
              <span class="cuisine">🍽️ {{ restaurant.types?.join(', ') || 'N/A' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Set page meta
useHead({
  title: 'Estimated Time Test - Asian Tables',
})

const restaurantsStore = useRestaurantsStore()
const { restaurants, getEstimatedTimeForDistance, updateAllEstimatedTimes } = restaurantsStore
useFirestoreRealtime('places', true, (places) => restaurantsStore.setRestaurants(places))

const trafficMultiplier = ref(1)

// Test cases for different distance and cuisine combinations
const testCases = computed(() => [
  {
    id: 1,
    name: "Nearby Vietnamese",
    distance: 0.5,
    cuisine: ['vietnamese'],
    estimatedTime: getEstimatedTimeForDistance(0.5, ['vietnamese'])
  },
  {
    id: 2,
    name: "Moderate Distance Japanese",
    distance: 2.0,
    cuisine: ['japanese'],
    estimatedTime: getEstimatedTimeForDistance(2.0, ['japanese'])
  },
  {
    id: 3,
    name: "Far Indian Restaurant",
    distance: 5.0,
    cuisine: ['indian'],
    estimatedTime: getEstimatedTimeForDistance(5.0, ['indian'])
  },
  {
    id: 4,
    name: "Quick Thai Food",
    distance: 1.2,
    cuisine: ['thai'],
    estimatedTime: getEstimatedTimeForDistance(1.2, ['thai'])
  },
  {
    id: 5,
    name: "Korean BBQ",
    distance: 3.5,
    cuisine: ['korean'],
    estimatedTime: getEstimatedTimeForDistance(3.5, ['korean'])
  },
  {
    id: 6,
    name: "Chinese Takeout",
    distance: 0.8,
    cuisine: ['chinese'],
    estimatedTime: getEstimatedTimeForDistance(0.8, ['chinese'])
  }
])

const updateTrafficConditions = () => {
  updateAllEstimatedTimes(trafficMultiplier.value)
}
</script>

<style lang="scss" scoped>
.test-page {
  min-height: 100vh;
  background: $bg-primary;
  padding: $spacing-xxl 0;
}

.container {
  max-width: $large-desktop;
  margin: 0 auto;
  padding: 0 $spacing-lg;
}

h1 {
  font-family: $font-primary;
  font-size: $font-4xl;
  color: $text-primary;
  text-align: center;
  margin-bottom: $spacing-xxl;
}

.test-section {
  margin-bottom: $spacing-xxl;
  
  h2 {
    font-family: $font-primary;
    font-size: $font-2xl;
    color: $text-primary;
    margin-bottom: $spacing-lg;
    border-bottom: 2px solid $primary-orange;
    padding-bottom: $spacing-sm;
  }
}

.test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: $spacing-lg;
}

.test-card {
  background: $white;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  box-shadow: $shadow-light;
  border: 1px solid $border-light;
  
  h3 {
    font-family: $font-primary;
    color: $primary-orange;
    margin-bottom: $spacing-md;
  }
  
  p {
    margin-bottom: $spacing-sm;
    font-family: $font-secondary;
    
    strong {
      color: $text-primary;
    }
  }
  
  .estimated-time {
    background: $primary-orange;
    color: $white;
    padding: $spacing-xs $spacing-sm;
    border-radius: $border-radius-sm;
    font-weight: $font-semibold;
  }
}

.traffic-controls {
  background: $white;
  padding: $spacing-lg;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-light;
  margin-bottom: $spacing-lg;
  
  label {
    display: block;
    font-family: $font-secondary;
    font-weight: $font-semibold;
    margin-bottom: $spacing-md;
    color: $text-primary;
  }
  
  .traffic-slider {
    width: 100%;
    margin-bottom: $spacing-sm;
  }
  
  .traffic-labels {
    display: flex;
    justify-content: space-between;
    font-size: $font-sm;
    color: $text-secondary;
  }
}

.update-btn {
  background: $primary-orange;
  color: $white;
  border: none;
  padding: $spacing-md $spacing-lg;
  border-radius: $border-radius-lg;
  font-family: $font-secondary;
  font-weight: $font-semibold;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: color.adjust($primary-orange, $lightness: -10%);
    transform: translateY(-2px);
  }
}

.restaurant-list {
  display: grid;
  gap: $spacing-md;
}

.restaurant-item {
  background: $white;
  padding: $spacing-lg;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-light;
  border-left: 4px solid $primary-orange;
  
  h4 {
    font-family: $font-primary;
    color: $text-primary;
    margin-bottom: $spacing-sm;
  }
  
  .restaurant-details {
    display: flex;
    gap: $spacing-lg;
    flex-wrap: wrap;
    
    span {
      font-family: $font-secondary;
      font-size: $font-sm;
      color: $text-secondary;
      
      &.time {
        color: $primary-orange;
        font-weight: $font-semibold;
      }
    }
  }
}

@media (max-width: $tablet) {
  .test-grid {
    grid-template-columns: 1fr;
  }
  
  .restaurant-details {
    flex-direction: column;
    gap: $spacing-xs;
  }
}
</style>
