<script setup lang="ts">
import type { Restaurant } from '~/types';


const { data, error, loading } = useFirestoreRealtime<Restaurant>('places');
const restaurants = data as unknown as Restaurant[] | null;

</script>

<template>
    <div class="p-4">

        <p v-if="loading">Loading...</p>
        <p v-if="error">Error: {{ error.message }}</p>

        <ul v-if="restaurants">
            <li v-for="place in restaurants" :key="place.id">
                {{ place.name }}
            </li>
        </ul>

    </div>
</template>