<script setup lang="ts">
import type { Restaurant } from '~/types';


const { data, error, loading } = useFirestoreRealtime<Restaurant>('places');


const handleClick = async () => {
    // console.log(data.value)
    await $fetch(`/api/restaurant/batch`, {
        method: 'PATCH',
    })
}
</script>

<template>
    <div class="p-4">
        <p v-if="loading">Loading...</p>
        <p v-if="error">Error: {{ error.message }}</p>
        <button @click="handleClick">Click</button>

    </div>
</template>