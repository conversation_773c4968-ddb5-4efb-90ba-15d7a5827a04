<template>
  <div class="browse-page">
    <!-- Header Section -->
    <section class="browse-header">
      <div class="container">
        <div class="browse-header__content">
          <div class="browse-header__back">
            <button class="browse-header__back-btn" @click="goBack">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="m15 18-6-6 6-6" />
              </svg>
              Back to Home
            </button>
          </div>
          <h1 class="browse-header__title">
            {{ pageTitle }}
          </h1>
          <p class="browse-header__subtitle">
            {{ pageSubtitle }}
          </p>
        </div>
      </div>
    </section>

    <!-- Search and Filters -->
    <section class="browse-filters">
      <div class="container">
        <SearchBar :initial-query="searchQuery" :initial-cuisine="selectedCuisine" :initial-price="selectedPrice"
          :initial-region="selectedRegion" />
      </div>
    </section>

    <!-- Restaurants Grid -->
    <section class="browse-restaurants">
      <div class="container">
        <div class="browse-restaurants__header">
          <div class="browse-restaurants__info">
            <h2 class="browse-restaurants__title">
              {{ totalItems }} Restaurants Found
            </h2>
            <p class="browse-restaurants__range" v-if="totalItems > 0">
              Showing {{ itemRangeStart }}-{{ itemRangeEnd }} of {{ totalItems }} restaurants
            </p>
            <button v-if="hasActiveFilters" @click="clearAllFilters" class="browse-restaurants__clear-filters">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <line x1="18" y1="6" x2="6" y2="18" />
                <line x1="6" y1="6" x2="18" y2="18" />
              </svg>
              Clear Filters
            </button>
            <div v-for="(filter, index) in searchFilter" :key="index">
              <button v-show="filter.value" @click="clearSingleFilter(filter)"
                class="browse-restaurants__clear-filters">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <line x1="18" y1="6" x2="6" y2="18" />
                  <line x1="6" y1="6" x2="18" y2="18" />
                </svg>
                {{ filter.label }}: {{ filter.value }}
                <!-- {{ filter }} -->
              </button>
            </div>
          </div>

          <div class="browse-restaurants__controls">
            <!-- View Mode Toggle -->
            <div class="browse-restaurants__view-toggle">
              <button class="browse-restaurants__view-btn"
                :class="{ 'browse-restaurants__view-btn--active': viewMode === 'grid' }" @click="viewMode = 'grid'"
                title="Grid View">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <rect x="3" y="3" width="7" height="7" />
                  <rect x="14" y="3" width="7" height="7" />
                  <rect x="14" y="14" width="7" height="7" />
                  <rect x="3" y="14" width="7" height="7" />
                </svg>
              </button>
              <button class="browse-restaurants__view-btn"
                :class="{ 'browse-restaurants__view-btn--active': viewMode === 'list' }" @click="viewMode = 'list'"
                title="List View">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <line x1="8" y1="6" x2="21" y2="6" />
                  <line x1="8" y1="12" x2="21" y2="12" />
                  <line x1="8" y1="18" x2="21" y2="18" />
                  <line x1="3" y1="6" x2="3.01" y2="6" />
                  <line x1="3" y1="12" x2="3.01" y2="12" />
                  <line x1="3" y1="18" x2="3.01" y2="18" />
                </svg>
              </button>
            </div>

            <!-- Items per page -->
            <div class="browse-restaurants__per-page">
              <label for="items-per-page">Show:</label>
              <select id="items-per-page" v-model="itemsPerPage" class="browse-restaurants__per-page-select">
                <option v-for="option in itemsPerPageOptions" :key="option" :value="option">
                  {{ option }}
                </option>
              </select>
            </div>

            <!-- Sort -->
            <div class="browse-restaurants__sort">
              <label for="sort-by">Sort by:</label>
              <select id="sort-by" v-model="sortBy" class="browse-restaurants__sort-select">
                <option value="distance">Distance</option>
                <option value="rating">Rating</option>
                <option value="name">Name</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Restaurant Display -->
        <div v-if="sortedRestaurants.length > 0">
          <!-- Grid View -->
          <div v-if="viewMode === 'grid'" class="browse-restaurants__grid">
            <RestaurantCard v-for="restaurant in paginatedRestaurants" :key="restaurant.id" :restaurant="restaurant"
              @click="handleRestaurantClick(restaurant)" />
          </div>

          <!-- List View -->
          <div v-if="viewMode === 'list'" class="browse-restaurants__list">
            <RestaurantListItem v-for="restaurant in paginatedRestaurants" :key="restaurant.id" :restaurant="restaurant"
              @click="handleRestaurantClick(restaurant)" />
          </div>
        </div>

        <!-- Empty State -->
        <ErrorState v-else type="404" title="No restaurants found"
          message="We couldn't find any restaurants matching your criteria."
          details="Try adjusting your search filters or browse all restaurants." :show-retry="false"
          :show-go-back="false" :show-go-home="false" :fullscreen="false">
          <template #actions>
            <button @click="clearAllFilters" class="browse-error-btn browse-error-btn--primary">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <line x1="18" y1="6" x2="6" y2="18" />
                <line x1="6" y1="6" x2="18" y2="18" />
              </svg>
              Clear Filters
            </button>
            <button @click="navigateTo('/')" class="browse-error-btn browse-error-btn--secondary">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
                <polyline points="9,22 9,12 15,12 15,22" />
              </svg>
              Browse All
            </button>
          </template>
        </ErrorState>

        <!-- Pagination -->
        <div class="browse-restaurants__pagination" v-if="totalPages > 1">


          <!-- Previous Button -->
          <button class="browse-restaurants__page-btn"
            :class="{ 'browse-restaurants__page-btn--disabled': currentPage === 1 }" @click="goToPage(currentPage - 1)"
            :disabled="currentPage === 1">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="m15 18-6-6 6-6" />
            </svg>
            Previous
          </button>
          <!-- First Page Button -->
          <button class="browse-restaurants__page-btn browse-restaurants__page-btn--first"
            :class="{ 'browse-restaurants__page-btn--disabled': currentPage === 1 }" @click="goToPage(1)"
            :disabled="currentPage === 1" title="First Page">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="m11 17-5-5 5-5" />
              <path d="m18 17-5-5 5-5" />
            </svg>
            1
          </button>

          <!-- Page Numbers -->
          <div class="browse-restaurants__page-numbers">
            <button v-for="page in visiblePages" :key="page" class="browse-restaurants__page-btn"
              :class="{ 'browse-restaurants__page-btn--active': page === currentPage }" @click="goToPage(page)">
              {{ page }}
            </button>
          </div>
          <!-- Last Page Button -->
          <button class="browse-restaurants__page-btn browse-restaurants__page-btn--last"
            :class="{ 'browse-restaurants__page-btn--disabled': currentPage === totalPages }"
            @click="goToPage(totalPages)" :disabled="currentPage === totalPages" title="Last Page">
            {{ totalPages }}
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="m6 17 5-5-5-5" />
              <path d="m13 17 5-5-5-5" />
            </svg>
          </button>

          <!-- Next Button -->
          <button class="browse-restaurants__page-btn"
            :class="{ 'browse-restaurants__page-btn--disabled': currentPage === totalPages }"
            @click="goToPage(currentPage + 1)" :disabled="currentPage === totalPages">
            Next
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="m9 18 6-6-6-6" />
            </svg>
          </button>


        </div>

        <!-- Pagination Info -->
        <div class="browse-restaurants__pagination-info" v-if="totalPages > 1">
          <p>Page {{ currentPage }} of {{ totalPages }}</p>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import type { Filter } from '@/types/index'
import { useFetchFirebaseData } from '~/composables/useFetchFirebaseData'
// Set page meta
useHead({
  title: 'Browse Restaurants - Asian Tables',
  meta: [
    {
      name: 'description',
      content: 'Browse and discover all Asian restaurants in your area. Filter by cuisine, price, and location to find your perfect dining experience.'
    }
  ]
})

const restaurantsStore = useRestaurantsStore()
const { restaurants } = storeToRefs(restaurantsStore)
// useFirestoreRealtime('places', true, (places) => restaurantsStore.setRestaurants(places))

// Get URL query parameters
const route = useRoute()

// Pagination and View Settings
const currentPage = ref(1)
const itemsPerPage = ref(12) // Increased default items per page
const sortBy = ref('distance')
const viewMode = ref('grid') // 'grid' or 'list'

// Items per page options
const itemsPerPageOptions = [6, 12, 24, 48]

// Search filters from URL parameters (reactive to route changes)
const searchQuery = ref(route.query.query || '')
const selectedCuisine = ref(route.query.cuisine || '')
const selectedPrice = ref(route.query.price || '')
const selectedRegion = ref(route.query.region || '')

// Search tags container
const searchFilter = computed<Filter[]>(() => {
  return [
    { label: 'Search', value: searchQuery.value as string },
    { label: 'Cuisine', value: selectedCuisine.value as string },
    { label: 'Price', value: selectedPrice.value as string },
    { label: 'Region', value: selectedRegion.value as string }
  ].filter(f => f.value)
})

// Watch for route query changes and update filters
watch(() => route.query, (newQuery) => {
  searchQuery.value = newQuery.query || ''
  selectedCuisine.value = newQuery.cuisine || ''
  selectedPrice.value = newQuery.price || ''
  selectedRegion.value = newQuery.region || ''

  // Reset to first page when query changes
  currentPage.value = 1
}, { immediate: false })

// Always show all restaurants
const baseRestaurants = computed(() => {
  return restaurants.value
})

// Filter restaurants based on search criteria
const filteredRestaurants = computed(() => {
  let filtered = [...baseRestaurants.value]

  // Filter by search query (name)
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(restaurant =>
      restaurant.name.toLowerCase().includes(query)
    )
  }

  // Filter by cuisine
  if (selectedCuisine.value) {
    filtered = filtered.filter(restaurant =>
      restaurant.types?.some(type =>
        type.toLowerCase().includes(selectedCuisine.value.toLowerCase())
      ) || false
    )
  }

  // Filter by price range
  if (selectedPrice.value) {
    filtered = filtered.filter(restaurant =>
      restaurant.priceRange === selectedPrice.value
    )
  }

  // Filter by region (if we had region data)
  if (selectedRegion.value) {
    // This would need region data in the restaurant object
    // For now, we'll skip this filter
  }

  return filtered
})

// Dynamic page title and subtitle
const pageTitle = computed(() => {
  if (selectedCuisine.value) {
    return `${selectedCuisine.value} Restaurants`
  }
  if (searchQuery.value) {
    return `Search Results for "${searchQuery.value}"`
  }
  return 'Browse All Restaurants'
})

const pageSubtitle = computed(() => {
  if (selectedCuisine.value) {
    return `Discover authentic ${selectedCuisine.value} cuisine in your area`
  }
  if (searchQuery.value) {
    return `Find the perfect restaurant that matches your search`
  }
  return 'Discover more amazing Asian restaurants in your area'
})

// Sorting logic
const sortedRestaurants = computed(() => {
  const sorted = [...filteredRestaurants.value]

  switch (sortBy.value) {
    case 'rating':
      return sorted.sort((a, b) => b.rating - a.rating)
    case 'name':
      return sorted.sort((a, b) => a.name.localeCompare(b.name))
    case 'distance':
    default:
      return sorted.sort((a, b) => {
        const distA = parseFloat(a.distance?.replace(' km', '') || '0')
        const distB = parseFloat(b.distance?.replace(' km', '') || '0')
        return distA - distB
      })
  }
})

// Pagination logic
const totalPages = computed(() => {
  return Math.ceil(sortedRestaurants.value.length / itemsPerPage.value)
})

const paginatedRestaurants = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value
  const end = start + itemsPerPage.value
  return sortedRestaurants.value.slice(start, end)
})

// Item count information
const itemRangeStart = computed(() => {
  if (sortedRestaurants.value.length === 0) return 0
  return (currentPage.value - 1) * itemsPerPage.value + 1
})

const itemRangeEnd = computed(() => {
  const end = currentPage.value * itemsPerPage.value
  return Math.min(end, sortedRestaurants.value.length)
})

const totalItems = computed(() => sortedRestaurants.value.length)

// Check if there are active filters
const hasActiveFilters = computed(() => {
  return !!(searchQuery.value || selectedCuisine.value || selectedPrice.value || selectedRegion.value)
})

const visiblePages = computed(() => {
  const pages = []
  const total = totalPages.value
  const current = currentPage.value

  // Show up to 5 page numbers
  let start = Math.max(1, current - 2)
  let end = Math.min(total, start + 4)

  // Adjust start if we're near the end
  if (end - start < 4) {
    start = Math.max(1, end - 4)
  }

  for (let i = start; i <= end; i++) {
    pages.push(i)
  }

  return pages
})

const goToPage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
    // Scroll to top when changing pages
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }
}

const goBack = () => {
  navigateTo('/')
}

const handleRestaurantClick = (restaurant) => {
  console.log('Selected restaurant:', restaurant.name)
  // Handle navigation to restaurant page
}

// Clear all filters and redirect to browse page without query params
const clearAllFilters = () => {
  navigateTo('/browse')
}

const clearSingleFilter = (filter: Filter) => {
  filter.value = null
}


// Reset to first page when sort or items per page changes
watch(sortBy, () => {
  currentPage.value = 1
})

watch(itemsPerPage, () => {
  currentPage.value = 1
})
</script>

<style lang="scss" scoped>
.browse-page {
  min-height: 100vh;
  background: $bg-primary;
}

.browse-header {
  padding: $spacing-xxl 0 $spacing-xl 0;
  background: linear-gradient(135deg, $primary-orange 0%, color.adjust($primary-orange, $lightness: 10%) 100%);
  color: $white;

  @media (max-width: $tablet) {
    padding: $spacing-xl 0 $spacing-lg 0;
  }

  &__content {
    text-align: center;
    position: relative;
  }

  &__back {
    position: absolute;
    left: 0;
    top: 0;

    @media (max-width: $tablet) {
      position: static;
      margin-bottom: $spacing-lg;
    }
  }

  &__back-btn {
    display: flex;
    align-items: center;
    gap: $spacing-sm;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: $white;
    padding: $spacing-sm $spacing-md;
    border-radius: $border-radius-lg;
    font-family: $font-secondary;
    font-size: $font-sm;
    font-weight: $font-medium;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);

    svg {
      width: 16px;
      height: 16px;
    }

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      border-color: rgba(255, 255, 255, 0.5);
      transform: translateX(-2px);
    }

    @media (max-width: $tablet) {
      align-self: flex-start;
    }
  }

  &__title {
    font-family: $font-primary;
    font-size: $font-4xl;
    font-weight: $font-bold;
    margin-bottom: $spacing-md;

    @media (max-width: $tablet) {
      font-size: $font-3xl;
    }
  }

  &__subtitle {
    font-family: $font-secondary;
    font-size: $font-lg;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
  }
}

.browse-filters {
  padding: $spacing-xl 0;
  background: $white;
  border-bottom: 1px solid $border-light;

  @media (max-width: $tablet) {
    padding: $spacing-lg 0;
  }
}

.browse-restaurants {
  padding: $spacing-xxl 0;

  @media (max-width: $tablet) {
    padding: $spacing-xl 0;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: $spacing-xxl;
    gap: $spacing-lg;

    @media (max-width: $tablet) {
      flex-direction: column;
      align-items: stretch;
    }
  }

  &__info {
    flex: 1;
  }

  &__title {
    font-family: $font-primary;
    font-size: $font-2xl;
    font-weight: $font-semibold;
    color: $text-primary;
    margin: 0 0 $spacing-xs 0;

    @media (max-width: $tablet) {
      font-size: $font-xl;
    }
  }

  &__range {
    font-family: $font-secondary;
    font-size: $font-sm;
    color: $text-secondary;
    margin: 0;
  }

  &__clear-filters {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    background: transparent;
    border: 1px solid $border-light;
    color: $text-secondary;
    padding: $spacing-xs $spacing-sm;
    border-radius: $border-radius-md;
    font-family: $font-secondary;
    font-size: $font-xs;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: $spacing-sm;

    svg {
      width: 12px;
      height: 12px;
    }

    &:hover {
      background: $bg-secondary;
      border-color: $primary-orange;
      color: $primary-orange;
    }
  }

  &__controls {
    display: flex;
    align-items: center;
    gap: $spacing-lg;
    flex-wrap: wrap;

    @media (max-width: $tablet) {
      justify-content: space-between;
    }

    @media (max-width: $mobile) {
      flex-direction: column;
      align-items: stretch;
      gap: $spacing-md;
    }
  }

  &__view-toggle {
    display: flex;
    border: 1px solid $border-light;
    border-radius: $border-radius-md;
    overflow: hidden;
  }

  &__view-btn {
    padding: $spacing-sm;
    background: $white;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    svg {
      width: 16px;
      height: 16px;
      color: $text-secondary;
    }

    &:hover {
      background: $bg-secondary;
    }

    &--active {
      background: $primary-orange;

      svg {
        color: $white;
      }
    }
  }

  &__per-page,
  &__sort {
    display: flex;
    align-items: center;
    gap: $spacing-sm;

    label {
      font-family: $font-secondary;
      font-size: $font-sm;
      color: $text-secondary;
      white-space: nowrap;
    }
  }

  &__per-page-select,
  &__sort-select {
    padding: $spacing-sm $spacing-md;
    border: 1px solid $border-light;
    border-radius: $border-radius-md;
    font-family: $font-secondary;
    font-size: $font-sm;
    background: $white;
    cursor: pointer;
    min-width: 80px;

    &:focus {
      outline: none;
      border-color: $primary-orange;
    }
  }

  &__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: $spacing-xl;
    margin-bottom: $spacing-xxl;

    @media (max-width: $tablet) {
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: $spacing-lg;
    }

    @media (max-width: $mobile) {
      grid-template-columns: 1fr;
      gap: $spacing-lg;
    }
  }

  &__list {
    display: flex;
    flex-direction: column;
    gap: $spacing-lg;
    margin-bottom: $spacing-xxl;
  }

  &__empty {
    text-align: center;
    padding: $spacing-xxl;
    color: $text-secondary;

    h3 {
      font-family: $font-primary;
      font-size: $font-xl;
      color: $text-primary;
      margin: $spacing-lg 0 $spacing-md 0;
    }

    p {
      font-family: $font-secondary;
      font-size: $font-base;
      max-width: 400px;
      margin: 0 auto;
    }
  }

  &__empty-icon {
    width: 64px;
    height: 64px;
    margin: 0 auto $spacing-lg auto;
    color: $text-muted;

    svg {
      width: 100%;
      height: 100%;
    }
  }

  &__pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: $spacing-sm;
    margin-top: $spacing-xxl;

    @media (max-width: $mobile) {
      flex-wrap: wrap;
      gap: $spacing-xs;
    }
  }

  &__page-numbers {
    display: flex;
    gap: $spacing-xs;
  }

  &__page-btn {
    padding: $spacing-sm $spacing-md;
    border: 1px solid $border-light;
    background: $white;
    color: $text-primary;
    border-radius: $border-radius-md;
    font-family: $font-secondary;
    font-size: $font-sm;
    font-weight: $font-medium;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: $spacing-xs;

    svg {
      width: 14px;
      height: 14px;
    }

    &:hover:not(&--disabled) {
      background: $primary-orange;
      color: $white;
      border-color: $primary-orange;
    }

    &--active {
      background: $primary-orange;
      color: $white;
      border-color: $primary-orange;
    }

    &--disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    &--first,
    &--last {
      font-weight: $font-semibold;

      @media (max-width: $mobile) {
        padding: $spacing-sm;

        span {
          display: none;
        }
      }
    }
  }

  &__pagination-info {
    text-align: center;
    margin-top: $spacing-lg;

    p {
      font-family: $font-secondary;
      font-size: $font-sm;
      color: $text-secondary;
      margin: 0;
    }
  }
}

.container {
  max-width: $large-desktop;
  margin: 0 auto;
  padding: 0 $spacing-lg;

  @media (max-width: $tablet) {
    padding: 0 $spacing-md;
  }
}

// Custom error button styles for browse page
.browse-error-btn {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  padding: $spacing-md $spacing-lg;
  border-radius: $border-radius-lg;
  font-family: $font-secondary;
  font-size: $font-base;
  font-weight: $font-semibold;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;

  svg {
    width: 16px;
    height: 16px;
  }

  &--primary {
    background: $primary-orange;
    color: $white;
    border: none;

    &:hover {
      background: color.adjust($primary-orange, $lightness: -10%);
      transform: translateY(-2px);
      box-shadow: $shadow-medium;
    }
  }

  &--secondary {
    background: transparent;
    color: $text-secondary;
    border: 1px solid $border-light;

    &:hover {
      background: $bg-secondary;
      border-color: $primary-orange;
      color: $primary-orange;
    }
  }

  @media (max-width: $mobile) {
    width: 100%;
    max-width: 200px;
    justify-content: center;
  }
}
</style>
