<template>
  <div class="homepage">
    <HomepageHeroSection />
    <HomepageFeaturedCuisines />
    <HomepagePopularRestaurants />
    <RestaurantOwnerCTA />
  </div>
</template>

<script setup>
// Set page meta
useHead({
  title: 'Asian Tables - Discover the Best Asian Restaurants',
  meta: [
    {
      name: 'description',
      content: 'Find the best Asian restaurants, dishes, and cuisines near you. Explore Korean, Japanese, Chinese, Thai, Vietnamese, and more authentic Asian dining experiences.'
    },
    {
      property: 'og:title',
      content: 'Asian Tables - Discover the Best Asian Restaurants'
    },
    {
      property: 'og:description',
      content: 'Find the best Asian restaurants, dishes, and cuisines near you. Explore Korean, Japanese, Chinese, Thai, Vietnamese, and more authentic Asian dining experiences.'
    },
    {
      property: 'og:type',
      content: 'website'
    }
  ]
})
const restaurantsStore = useRestaurantsStore()
useFirestoreRealtime('places', true, (places) => restaurantsStore.setRestaurants(places))
</script>

<style lang="scss" scoped>
.homepage {
  min-height: 100vh;
  // background-color: $bg-primary;
}
</style>
