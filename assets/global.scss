@use "variables" as *;
@use "fonts" as *;

// Reset and base styles
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: $font-secondary;
  font-size: $font-base;
  font-weight: $font-regular;
  line-height: $leading-normal;
  color: $text-primary;
  background-color: $bg-primary;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// Typography
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: $font-primary;
  font-weight: $font-semibold;
  line-height: $leading-tight;
  margin: 0;
}

p {
  margin: 0;
}

a {
  color: inherit;
  text-decoration: none;
}

button {
  font-family: inherit;
  cursor: pointer;
}

img {
  max-width: 100%;
  height: auto;
}

// Utility classes
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// Mobile-specific improvements
@media (max-width: 480px) {
  // Improve touch targets
  button,
  .button,
  a[role="button"] {
    min-height: 44px;
    min-width: 44px;
  }

  // Better text sizing for mobile
  h1 {
    font-size: $font-3xl !important;
    line-height: $leading-tight;
  }

  h2 {
    font-size: $font-2xl !important;
    line-height: $leading-tight;
  }

  h3 {
    font-size: $font-xl !important;
  }

  // Improve form elements
  input,
  select,
  textarea {
    font-size: 16px; // Prevents zoom on iOS
  }

  // Better spacing for mobile
  .container {
    padding-left: $spacing-md;
    padding-right: $spacing-md;
  }
}
