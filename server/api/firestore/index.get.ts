import { useFirestore } from '~/server/utils/firebaseFirestore'

export default defineEventHandler(async (event) => {
    const { path } = getQuery(event) as { path: string }
    const pathObject = JSON.parse(path)

    try{
        const data = await fetchData(pathObject.ref)
        return{
            status: 200,
            body: data,

        }
    }catch (error){
        console.error(`Error fetching ${pathObject.type}:`, error)
        return {
            status: 500,
            message: "Internal Server Error"
        }
    }
})

async function fetchData(path:string) {
    const { firestoreCollectionFetch } = useFirestore()
    return firestoreCollectionFetch(path)
}