import { GeoPoint } from "firebase-admin/firestore";

export default defineEventHandler(async (event) => {
  try {
    const queryParams = getQuery(event);
    const { lat, lng, limit = 9, lastDoc } = queryParams;

    if (!lat || !lng) {
      throw createError({
        statusCode: 400,
        statusMessage: "lat and lng are required",
      });
    }

    const userLat = parseFloat(lat as string);
    const userLng = parseFloat(lng as string);
    const limitNum = parseInt(limit as string);

    console.log('Fetching nearby restaurants for:', { userLat, userLng, limitNum });

    const { fs } = useFirebaseAdmin();

    // For geospatial queries, we'll use a bounding box approach
    // Calculate approximate bounding box (roughly 50km radius)
    const latDelta = 0.45; // Approximately 50km
    const lngDelta = 0.45; // Approximately 50km

    const minLat = userLat - latDelta;
    const maxLat = userLat + latDelta;
    const minLng = userLng - lngDelta;
    const maxLng = userLng + lngDelta;

    console.log('Bounding box:', { minLat, maxLat, minLng, maxLng });

    // Build the query
    let query = fs.collection("places")
      .where("location", ">=", new GeoPoint(minLat, minLng))
      .where("location", "<=", new GeoPoint(maxLat, maxLng))
      .limit(limitNum * 3); // Get more to filter and sort

    // Add pagination if lastDoc is provided
    if (lastDoc) {
      const lastDocRef = await fs.collection("places").doc(lastDoc as string).get();
      if (lastDocRef.exists) {
        query = query.startAfter(lastDocRef);
      }
    }

    const snapshot = await query.get();
    console.log(`Found ${snapshot.docs.length} restaurants in bounding box`);

    if (snapshot.empty) {
      return {
        data: [],
        hasMore: false,
        lastDoc: null,
      };
    }

    // Calculate distances and sort
    const restaurantsWithDistance = snapshot.docs.map((doc) => {
      const data = doc.data();
      const restaurantLocation = data.location as GeoPoint;
      
      // Calculate distance using Haversine formula
      const distance = calculateDistance(
        userLat,
        userLng,
        restaurantLocation.latitude,
        restaurantLocation.longitude
      );

      return {
        id: doc.id,
        ...data,
        distance: `${distance.toFixed(1)} km`,
        distanceKm: distance,
      };
    })
    .sort((a, b) => a.distanceKm - b.distanceKm)
    .slice(0, limitNum); // Take only the requested number

    // Remove the temporary distanceKm field
    const restaurants = restaurantsWithDistance.map(({ distanceKm, ...restaurant }) => restaurant);

    const hasMore = snapshot.docs.length >= limitNum * 3; // Rough estimate
    const lastDocId = hasMore && restaurants.length > 0 
      ? snapshot.docs[snapshot.docs.length - 1].id 
      : null;

    console.log(`Returning ${restaurants.length} nearby restaurants`);

    return {
      data: restaurants,
      hasMore,
      lastDoc: lastDocId,
    };

  } catch (error) {
    console.error("Error fetching nearby restaurants:", error);

    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error("Error details:", errorMessage);

    throw createError({
      statusCode: 500,
      statusMessage: `Failed to fetch nearby restaurants: ${errorMessage}`,
    });
  }
});

// Haversine formula to calculate distance between two points
function calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
  const R = 6371; // Earth's radius in km
  const dLat = (lat2 - lat1) * (Math.PI / 180);
  const dLng = (lng2 - lng1) * (Math.PI / 180);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * (Math.PI / 180)) *
      Math.cos(lat2 * (Math.PI / 180)) *
      Math.sin(dLng / 2) *
      Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}
