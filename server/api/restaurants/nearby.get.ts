import { GeoPoint } from "firebase-admin/firestore";

export default defineEventHandler(async (event) => {
  try {
    const queryParams = getQuery(event);
    const { lat, lng, limit = 9, lastDoc } = queryParams;

    if (!lat || !lng) {
      throw createError({
        statusCode: 400,
        statusMessage: "lat and lng are required",
      });
    }

    const userLat = parseFloat(lat as string);
    const userLng = parseFloat(lng as string);
    const limitNum = parseInt(limit as string);

    console.log("Fetching nearby restaurants for:", {
      userLat,
      userLng,
      limitNum,
    });

    const { fs } = useFirebaseAdmin();

    // For geospatial queries, we'll use a bounding box approach
    // Calculate approximate bounding box (roughly 50km radius)
    const latDelta = 0.45; // Approximately 50km
    const lngDelta = 0.45; // Approximately 50km

    const minLat = userLat - latDelta;
    const maxLat = userLat + latDelta;
    const minLng = userLng - lngDelta;
    const maxLng = userLng + lngDelta;

    console.log("Bounding box:", { minLat, maxLat, minLng, maxLng });

    // Build the query - get more results to ensure we have enough after distance sorting
    // For pagination, we need to get a larger set to maintain consistent distance ordering
    const queryLimit = Math.max(limitNum * 8, 100); // Get enough data for proper sorting

    let query = fs
      .collection("places")
      .where("location", ">=", new GeoPoint(minLat, minLng))
      .where("location", "<=", new GeoPoint(maxLat, maxLng))
      .limit(queryLimit);

    console.log(
      `Query: Getting up to ${queryLimit} restaurants for sorting and pagination`
    );

    const snapshot = await query.get();
    console.log(`Found ${snapshot.docs.length} restaurants in bounding box`);

    if (snapshot.empty) {
      return {
        data: [],
        hasMore: false,
        lastDoc: null,
      };
    }

    // Calculate distances and sort
    const restaurantsWithDistance = snapshot.docs
      .map((doc) => {
        const data = doc.data();
        const restaurantLocation = data.location as GeoPoint;

        // Calculate distance using Haversine formula
        const distance = calculateDistance(
          userLat,
          userLng,
          restaurantLocation.latitude,
          restaurantLocation.longitude
        );

        return {
          id: doc.id,
          ...data,
          distance: `${distance.toFixed(1)} km`,
          distanceKm: distance,
        };
      })
      .sort((a, b) => a.distanceKm - b.distanceKm); // Sort by actual distance

    console.log(
      "All restaurants with distances:",
      restaurantsWithDistance.slice(0, 5).map((r: any) => ({
        name: r.name || r.id,
        distance: r.distance,
        distanceKm: r.distanceKm,
      }))
    );

    // Handle pagination with distance-sorted results
    let startIndex = 0;
    if (lastDoc) {
      // Find the index of the last document in our sorted results
      const lastDocIndex = restaurantsWithDistance.findIndex(
        (r) => r.id === lastDoc
      );
      if (lastDocIndex !== -1) {
        startIndex = lastDocIndex + 1;
        console.log(
          `Pagination: Found lastDoc at index ${lastDocIndex}, starting from ${startIndex}`
        );
      } else {
        console.log(
          `Pagination: lastDoc ${lastDoc} not found in current results, starting from 0`
        );
      }
    }

    // Get the requested slice, ensuring no duplicates
    const paginatedRestaurants = restaurantsWithDistance.slice(
      startIndex,
      startIndex + limitNum
    );

    console.log(
      `Pagination: Returning ${
        paginatedRestaurants.length
      } restaurants from index ${startIndex} to ${startIndex + limitNum - 1}`
    );
    console.log(
      "Restaurant IDs being returned:",
      paginatedRestaurants.map((r) => r.id)
    );

    // Remove the temporary distanceKm field
    const restaurants = paginatedRestaurants.map(
      ({ distanceKm, ...restaurant }) => restaurant
    );

    const hasMore = startIndex + limitNum < restaurantsWithDistance.length;
    const lastDocId =
      hasMore && restaurants.length > 0
        ? restaurants[restaurants.length - 1].id
        : null;

    console.log(`Returning ${restaurants.length} nearby restaurants`);
    console.log(
      `Pagination info: hasMore=${hasMore}, lastDocId=${lastDocId}, totalAvailable=${restaurantsWithDistance.length}`
    );
    console.log(
      "Returned restaurants distances:",
      restaurants.map((r: any) => ({
        name: r.name || r.id,
        distance: r.distance,
      }))
    );

    return {
      data: restaurants,
      hasMore,
      lastDoc: lastDocId,
    };
  } catch (error) {
    console.error("Error fetching nearby restaurants:", error);

    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    console.error("Error details:", errorMessage);

    throw createError({
      statusCode: 500,
      statusMessage: `Failed to fetch nearby restaurants: ${errorMessage}`,
    });
  }
});

// Haversine formula to calculate distance between two points
function calculateDistance(
  lat1: number,
  lng1: number,
  lat2: number,
  lng2: number
): number {
  const R = 6371; // Earth's radius in km
  const dLat = (lat2 - lat1) * (Math.PI / 180);
  const dLng = (lng2 - lng1) * (Math.PI / 180);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * (Math.PI / 180)) *
      Math.cos(lat2 * (Math.PI / 180)) *
      Math.sin(dLng / 2) *
      Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}
