import { FieldValue, GeoPoint } from "firebase-admin/firestore";

export default defineEventHandler(async (event) => {
  try {
    const { fs } = useFirebaseAdmin();
    let snapshot = await fs.collection("places").get();

    const batch = fs.batch();
    snapshot.forEach((doc) => {
      const data = doc.data();
      console.log(`Updating: ${doc.id}`);
      const coords = data.coordinates as { lat: number; lng: number };
      const newLocation = new GeoPoint(coords.lat, coords.lng);
      batch.update(doc.ref, {
        location: newLocation,
        coordinates: FieldValue.delete(),
      });
    });

    await batch.commit();
    console.log("Migration completed");
  } catch (error) {
    console.error("Error updating restaurant:", error);
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    throw createError({
      statusCode: 500,
      statusMessage: `Failed to update restaurant: ${errorMessage}`,
    });
  }

  // const restaurant = await updateRestaurant(id, body);

  // if (!restaurant) {
  //     throw createError({ statusCode: 404, statusMessage: 'Restaurant not found' });
  // }

  // return restaurant;
});
