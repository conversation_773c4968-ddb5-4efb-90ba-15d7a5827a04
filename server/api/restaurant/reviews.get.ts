export default defineEventHandler(async (event) => {
  try {
    const queryParams = getQuery(event);
    const { place_id, startAfter } = queryParams;

    if (!place_id) {
      throw createError({
        statusCode: 400,
        statusMessage: "place_id is required",
      });
    }

    console.log('Fetching reviews for place_id:', place_id);

    const {fs} = useFirebaseAdmin();
    let query = fs.collection("reviews").where("place_id", "==", place_id).orderBy("time", "desc").limit(3);
    if (startAfter && !isNaN(Number(startAfter))) {
      console.log('Last review snapshot:', startAfter);
      const seconds = Number(startAfter);
      query = query.startAfter(seconds);
    }


    const snapshot = await query.get();
    const data = snapshot.docs.map(doc => ({
      ...doc.data(),
      id: doc.id,
    }));

    const hasMore = data.length === 3 // Assuming we want to limit to 8 reviews per page
    const nextQuery = snapshot.docs[snapshot.docs.length - 1].get("time") || null;


    return {
      data,
      hasMore,
      nextQuery
    };

  } catch (error) {
    console.error("Error in reviews API:", error);

    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorCode = (error as any)?.code || 'UNKNOWN';

    console.error("Error details:", {
      message: errorMessage,
      code: errorCode
    });

    throw createError({
      statusCode: 500,
      statusMessage: `Failed to fetch reviews: ${errorMessage}`,
    });
  }
});
