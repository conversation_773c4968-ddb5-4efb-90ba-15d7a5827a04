export default defineEventHandler(async (event) => {
  const { place_id, lastReviewSnapshot } = await readBody(event);
  const query = fs
    .collection("reviews")
    .where("place_id", "==", place_id)
    .orderBy("created_at", "desc")
    .limit(8);

  if (lastReviewSnapshot) {
    query.startAfter(lastReviewSnapshot);
  }

  try {
    const snapshot = await query.get();
    if (snapshot.empty) {
      return {
        data: [],
      };
    } else {
      const reviews = snapshot.docs.map((doc) => {
        const data = doc.data();
        return {
          ...data,
          id: doc.id,
        };
      });
      const nextQuery = fs
        .collection("reviews")
        .where("place_id", "==", place_id)
        .orderBy("created_at", "desc")
        .startAfter(snapshot.docs[snapshot.docs.length - 1])
        .limit(8);

      return {
        data: reviews,
        nextQuery: nextQuery,
      };
    }
  } catch (error) {}
});
