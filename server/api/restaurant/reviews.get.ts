// import { useFirebaseAdmin } from "~/server/utils/firebase";
export default defineEventHandler(async (event) => {
  try {
    const queryParams = getQuery(event);
    const { place_id, lastReviewSnapshot } = queryParams;

    if (!place_id) {
      throw createError({
        statusCode: 400,
        statusMessage: "place_id is required",
      });
    }

    console.log('Fetching reviews for place_id:', place_id);

    // Temporary mock data while Firebase connection is being fixed
    // This maintains the exact same data structure expected by the client
    // const mockReviews = [
    //   {
    //     id: "review_1",
    //     author_name: "<PERSON>",
    //     createdAt: "2024-01-15T10:30:00Z",
    //     place_id: place_id as string,
    //     rating: 5,
    //     source: "google" as const,
    //     text: "Amazing food and great service! The atmosphere was perfect for a family dinner. Highly recommend the signature dishes.",
    //     time: 1705315800
    //   },
    //   {
    //     id: "review_2",
    //     author_name: "<PERSON>",
    //     createdAt: "2024-01-10T14:20:00Z",
    //     place_id: place_id as string,
    //     rating: 4,
    //     source: "google" as const,
    //     text: "Good food quality and reasonable prices. The staff was friendly and attentive. Will definitely come back.",
    //     time: 1704888000
    //   },
    //   {
    //     id: "review_3",
    //     author_name: "Mike Chen",
    //     createdAt: "2024-01-05T19:45:00Z",
    //     place_id: place_id as string,
    //     rating: 5,
    //     source: "google" as const,
    //     text: "Excellent authentic cuisine! The flavors were incredible and the presentation was beautiful. A must-visit restaurant.",
    //     time: 1704484500
    //   }
    // ];

    // Simulate pagination
    // const startIndex = lastReviewSnapshot ? parseInt(lastReviewSnapshot as string) || 0 : 0;
    // const endIndex = Math.min(startIndex + 8, mockReviews.length);
    // const paginatedReviews = mockReviews.slice(startIndex, endIndex);

    // const hasMore = endIndex < mockReviews.length;
    // const lastDoc = hasMore ? endIndex.toString() : null;

    // console.log('Returning mock reviews:', paginatedReviews.length, 'hasMore:', hasMore);

    const {fs} = useFirebaseAdmin();
    const query = fs.collection("reviews").where("place_id", "==", place_id)


    const snapshot = await query.get();
    const data = snapshot.docs.map(doc => ({
      ...doc.data(),
      id: doc.id,
    }));

    return {
      data 
    };

    // TODO: Replace with actual Firebase query once SSL/TLS issue is resolved
    /*
    console.log('Initializing Firebase Admin...');
    const { fs } = useFirebaseAdmin();
    console.log('Firebase Admin initialized successfully');

    console.log('Building Firestore query for place_id:', place_id);
    let firestoreQuery = fs
      .collection("reviews")
      .where("place_id", "==", place_id)
      .orderBy("time", "desc")
      .limit(8);

    if (lastReviewSnapshot) {
      console.log('Adding pagination with lastReviewSnapshot:', lastReviewSnapshot);
      firestoreQuery = firestoreQuery.startAfter(lastReviewSnapshot);
    }

    console.log('Executing Firestore query...');
    const snapshot = await firestoreQuery.get();
    console.log('Query executed successfully, docs count:', snapshot.docs.length);

    if (snapshot.empty) {
      console.log('No reviews found');
      return {
        data: [],
        hasMore: false,
        lastDoc: null,
      };
    }

    const reviews = snapshot.docs.map((doc) => {
      const data = doc.data();
      return {
        ...data,
        id: doc.id,
      };
    });

    // Check if there are more reviews
    const hasMore = snapshot.docs.length === 8;
    const lastDoc = hasMore ? snapshot.docs[snapshot.docs.length - 1] : null;

    console.log('Returning reviews:', reviews.length, 'hasMore:', hasMore);

    return {
      data: reviews,
      hasMore,
      lastDoc: lastDoc ? lastDoc.id : null,
    };
    */
  } catch (error) {
    console.error("Error in reviews API:", error);

    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorCode = (error as any)?.code || 'UNKNOWN';

    console.error("Error details:", {
      message: errorMessage,
      code: errorCode
    });

    throw createError({
      statusCode: 500,
      statusMessage: `Failed to fetch reviews: ${errorMessage}`,
    });
  }
});
