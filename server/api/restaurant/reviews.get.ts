import { useFirebaseAdmin } from "~/server/utils/firebase";

export default defineEventHandler(async (event) => {
  const queryParams = getQuery(event);
  const { place_id, lastReviewSnapshot } = queryParams;
  const { fs } = useFirebaseAdmin();

  if (!place_id) {
    throw createError({
      statusCode: 400,
      statusMessage: "place_id is required",
    });
  }

  let firestoreQuery = fs.collection("reviews").where("place_id", "==", place_id);

  if (lastReviewSnapshot) {
    firestoreQuery = firestoreQuery.startAfter(lastReviewSnapshot);
  }

  try {
    const snapshot = await firestoreQuery.get();
    if (snapshot.empty) {
      return {
        data: [],
        hasMore: false,
      };
    } else {
      const reviews = snapshot.docs.map((doc) => {
        const data = doc.data();
        return {
          ...data,
          id: doc.id,
        };
      });

      // Check if there are more reviews
      // const hasMore = snapshot.docs.length === 8;
      // const lastDoc = hasMore ? snapshot.docs[snapshot.docs.length - 1] : null;

      return {
        data: reviews,
        // hasMore,
        // lastDoc: lastDoc ? lastDoc.id : null,
      };
    }
  } catch (error) {
    console.error("Error fetching reviews:", error);
    throw createError({
      statusCode: 500,
      statusMessage: "Failed to fetch reviews",
    });
  }
});
