import { fs } from'./firebase'

export const useFirestore = () => {

    const firestoreCollectionFetch = async (collection: string) =>
  {
    const query = fs.collection(collection);
    const snapshot = await query.get();
    if (!snapshot.empty)
    {
      return snapshot.docs.map((doc) =>
      {
        const data = doc.data();
        return {
          ...data,
          id: doc.id,
        };
      });
    } else
    {
      throw new Error("No data found");
    }

    
  };
  return {
        firestoreCollectionFetch
    }
}