import { cert, getApp, initializeApp, ServiceAccount } from 'firebase-admin/app'
import { getFirestore, FieldPath } from 'firebase-admin/firestore'

const config = useRuntimeConfig()
const { firebaseAdmin } = config

const createFirebaseApp = () => {
    
    try{
        console.log(getApp())
        return getApp()
    } catch {
        
        // Create service account object from environment variables
        const serviceAccount: ServiceAccount = {
            projectId: firebaseAdmin.projectId,
            privateKey: firebaseAdmin.privateKey?.replace(/\\n/g, '\n'),
            clientEmail: firebaseAdmin.clientEmail,
        }

        return initializeApp({
            credential: cert(serviceAccount)
        })
    }
}

export const app = createFirebaseApp()
export const fs = getFirestore()
export const fsFieldPath = FieldPath