import { cert, initializeApp, getApps, getApp } from "firebase-admin/app";
import { getAuth } from "firebase-admin/auth";
import { getFirestore, FieldPath } from "firebase-admin/firestore";
import { getDatabase } from "firebase-admin/database";

export const useFirebaseAdmin = () => {
  const runtimeConfig = useRuntimeConfig();
  if (!getApps().length) {
    initializeApp({
      credential: cert({
        projectId: runtimeConfig.firebaseAdmin.projectId,
        clientEmail: runtimeConfig.firebaseAdmin.clientEmail,
        privateKey: runtimeConfig.firebaseAdmin.privateKey?.replace(
          /\\n/g,
          "\n"
        ),
      }),
      //   databaseURL: runtimeConfig.firebaseAdmin.databaseURL,
    });
  }
  return {
    auth: getAuth(getApp()),
    // db: getDatabase(),
    fs: getFirestore(),
    fsFieldPath: FieldPath,
  };
};
