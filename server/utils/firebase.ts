import { cert, initializeApp, getApps, getApp } from "firebase-admin/app";
import { getAuth } from "firebase-admin/auth";
import { getFirestore, FieldPath } from "firebase-admin/firestore";
import { getDatabase } from "firebase-admin/database";

export const useFirebaseAdmin = () => {
  try {
    const runtimeConfig = useRuntimeConfig();

    // Validate required configuration
    if (!runtimeConfig.firebaseAdmin?.projectId ||
        !runtimeConfig.firebaseAdmin?.clientEmail ||
        !runtimeConfig.firebaseAdmin?.privateKey) {
      throw new Error('Missing Firebase Admin configuration. Please check your environment variables.');
    }

    if (!getApps().length) {
      console.log('Initializing Firebase Admin app...');
      const serviceAccount = {
        projectId: runtimeConfig.firebaseAdmin.projectId,
        clientEmail: runtimeConfig.firebaseAdmin.clientEmail,
        privateKey: runtimeConfig.firebaseAdmin.privateKey.replace(/\\n/g, "\n"),
      };

      console.log('Service account configured for project:', serviceAccount.projectId);

      initializeApp({
        credential: cert(serviceAccount),
      });

      console.log('Firebase Admin app initialized successfully');
    }

    const app = getApp();
    const firestore = getFirestore(app);

    return {
      auth: getAuth(app),
      fs: firestore,
      fsFieldPath: FieldPath,
    };
  } catch (error) {
    console.error('Error initializing Firebase Admin:', error);
    throw error;
  }
};
