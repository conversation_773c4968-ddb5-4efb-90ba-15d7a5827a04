import { cert, initializeApp, getApps, getApp } from "firebase-admin/app";
import { getAuth } from "firebase-admin/auth";
import { getFirestore, FieldPath } from "firebase-admin/firestore";

export const useFirebaseAdmin = () => {
  const runtimeConfig = useRuntimeConfig();
  if (!getApps().length) {
    initializeApp({
      credential: cert({
        projectId: runtimeConfig.firebaseAdmin.projectId,
        clientEmail: runtimeConfig.firebaseAdmin.clientEmail,
        privateKey: runtimeConfig.firebaseAdmin.privateKey?.replace(/\\n/g, '\n'),
      }),
    });
  }
  return {
    auth: getAuth(getApp()),
    fs: getFirestore(),
    fsFieldPath: FieldPath,
  };
};