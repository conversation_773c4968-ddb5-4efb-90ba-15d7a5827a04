<template>
  <div class="error-page">
    <ErrorState 
      :type="errorType"
      :title="errorTitle"
      :message="errorMessage"
      :details="errorDetails"
      :fullscreen="true"
      :show-retry="showRetry"
      :show-go-back="true"
      :show-go-home="true"
      back-url="/browse"
      @retry="handleRetry"
      @go-back="navigateTo('/browse')"
      @go-home="navigateTo('/')"
    />
  </div>
</template>

<script setup lang="ts">
interface Props {
  error: any
}

const props = defineProps<Props>()

// Determine error type and content based on status code
const errorType = computed(() => {
  const statusCode = props.error?.statusCode || 500
  
  if (statusCode === 404) return '404'
  if (statusCode >= 500) return 'server'
  if (statusCode >= 400) return 'network'
  return 'generic'
})

const errorTitle = computed(() => {
  const statusCode = props.error?.statusCode || 500
  
  switch (statusCode) {
    case 404:
      return 'Page Not Found'
    case 500:
      return 'Server Error'
    case 503:
      return 'Service Unavailable'
    default:
      return `Error ${statusCode}`
  }
})

const errorMessage = computed(() => {
  const statusCode = props.error?.statusCode || 500
  
  switch (statusCode) {
    case 404:
      return "Sorry, we couldn't find the page you're looking for."
    case 500:
      return 'Our servers are experiencing issues.'
    case 503:
      return 'The service is temporarily unavailable.'
    default:
      return props.error?.statusMessage || 'Something went wrong.'
  }
})

const errorDetails = computed(() => {
  const statusCode = props.error?.statusCode || 500
  
  switch (statusCode) {
    case 404:
      return 'The page may have been moved, deleted, or you may have entered an incorrect URL.'
    case 500:
      return "We're working to fix this. Please try again in a few minutes."
    case 503:
      return 'Please try again later.'
    default:
      return 'Please try refreshing the page or contact support if the problem persists.'
  }
})

const showRetry = computed(() => {
  const statusCode = props.error?.statusCode || 500
  return statusCode >= 500 // Show retry for server errors
})

// Set page meta
useHead({
  title: computed(() => `${errorTitle.value} - Asian Tables`),
  meta: [
    {
      name: 'description',
      content: computed(() => errorMessage.value)
    }
  ]
})

// Handle retry action
const handleRetry = () => {
  // Reload the page
  window.location.reload()
}

// Clear error on route change
watch(() => useRoute().path, () => {
  clearError()
})
</script>

<style lang="scss" scoped>
.error-page {
  min-height: 100vh;
  background: $bg-primary;
}
</style>
